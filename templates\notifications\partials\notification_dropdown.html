{% if notifications %}
    <div class="space-y-2">
        {% for notification in notifications %}
            <div class="flex items-start p-3 rounded-lg border hover:bg-gray-50 transition-colors
                {% if notification.notification_type == 'low_stock' or notification.notification_type == 'out_of_stock' %}bg-yellow-50 border-yellow-200
                {% elif notification.notification_type == 'request_approved' %}bg-green-50 border-green-200
                {% elif notification.notification_type == 'request_rejected' %}bg-red-50 border-red-200
                {% elif notification.notification_type|slice:':7' == 'request' %}bg-blue-50 border-blue-200
                {% else %}bg-gray-50 border-gray-200{% endif %}">
                
                <!-- Icon -->
                <div class="flex-shrink-0">
                    {% if notification.notification_type == 'out_of_stock' or notification.priority == 'urgent' %}
                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    {% elif notification.notification_type == 'low_stock' or notification.priority == 'high' %}
                        <svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    {% elif notification.notification_type == 'request_approved' %}
                        <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    {% elif notification.notification_type == 'request_rejected' %}
                        <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    {% else %}
                        <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    {% endif %}
                </div>
                
                <!-- Content -->
                <div class="ml-3 flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 {% if not notification.is_read %}font-semibold{% endif %}">
                        {{ notification.title|truncatechars:50 }}
                        {% if not notification.is_read %}
                            <span class="inline-block w-1.5 h-1.5 bg-blue-600 rounded-full ml-1"></span>
                        {% endif %}
                    </p>
                    <p class="text-xs text-gray-600 mt-1">{{ notification.message|truncatechars:80 }}</p>
                    <p class="text-xs text-gray-500 mt-1">{{ notification.created_at|timesince }} ago</p>
                    
                    {% if notification.action_url %}
                        <a href="{{ notification.action_url }}" 
                           class="text-xs text-blue-600 hover:text-blue-500 mt-1 inline-block">
                            {{ notification.action_text|default:"View" }} →
                        </a>
                    {% endif %}
                </div>
                
                <!-- Mark as read button -->
                {% if not notification.is_read %}
                    <button hx-post="{% url 'mark_notification_read' notification.id %}"
                            hx-target="closest div"
                            hx-swap="outerHTML"
                            class="ml-2 text-gray-400 hover:text-gray-600">
                        <span class="sr-only">Mark as read</span>
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                {% endif %}
            </div>
        {% endfor %}
    </div>
    
    <!-- View All Link -->
    <div class="border-t border-gray-200 pt-2 mt-2">
        <a href="{% url 'notification_center' %}" 
           class="block text-center text-sm text-blue-600 hover:text-blue-500 font-medium py-2">
            View All Notifications
            {% if unread_count > 5 %}
                ({{ unread_count|add:"-5" }} more)
            {% endif %}
        </a>
    </div>
{% else %}
    <div class="text-center py-6">
        <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
        <p class="text-sm text-gray-500">No new notifications</p>
        <a href="{% url 'notification_center' %}" 
           class="text-sm text-blue-600 hover:text-blue-500 mt-2 inline-block">
            View notification center
        </a>
    </div>
{% endif %}
