<!-- Summary Statistics -->
<div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-blue-600">{{ summary_stats.total_requests }}</p>
            <p class="text-sm text-gray-600">Total Requests</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-yellow-600">{{ summary_stats.pending_requests }}</p>
            <p class="text-sm text-gray-600">Pending</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-green-600">{{ summary_stats.approved_requests }}</p>
            <p class="text-sm text-gray-600">Approved</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-red-600">{{ summary_stats.rejected_requests }}</p>
            <p class="text-sm text-gray-600">Rejected</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-purple-600">{{ summary_stats.released_requests }}</p>
            <p class="text-sm text-gray-600">Released</p>
        </div>
    </div>
</div>

<!-- Department Breakdown -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Requests by Department</h3>
        <div class="space-y-3">
            {% for dept in dept_breakdown %}
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">{{ dept.department }}</p>
                    <p class="text-sm text-gray-600">{{ dept.count }} total requests</p>
                </div>
                <div class="flex space-x-2 text-xs">
                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">P: {{ dept.pending }}</span>
                    <span class="px-2 py-1 bg-green-100 text-green-800 rounded">A: {{ dept.approved }}</span>
                    <span class="px-2 py-1 bg-red-100 text-red-800 rounded">R: {{ dept.rejected }}</span>
                    <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded">L: {{ dept.released }}</span>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No department data available</p>
            {% endfor %}
        </div>
    </div>

    <!-- Daily Trends Chart Placeholder -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Daily Request Trends (Last 30 Days)</h3>
        <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            <div class="text-center">
                <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p class="text-gray-500">Chart visualization would go here</p>
                <p class="text-sm text-gray-400">{{ daily_stats|length }} days of data available</p>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Request List -->
<div class="bg-white rounded-lg shadow-md">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Request Details</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request #</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requester</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved Date</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for request in page_obj %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <a href="{% url 'request_detail' request.id %}" class="text-blue-600 hover:text-blue-800 font-medium">
                            {{ request.request_number }}
                        </a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ request.requester.get_full_name|default:request.requester.username }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ request.department }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            {% if request.status == 'pending' %}bg-yellow-100 text-yellow-800
                            {% elif request.status == 'approved' %}bg-green-100 text-green-800
                            {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                            {% elif request.status == 'released' %}bg-purple-100 text-purple-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ request.get_status_display }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ request.request_date|date:"M d, Y H:i" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {% if request.approved_date %}
                            {{ request.approved_date|date:"M d, Y H:i" }}
                        {% else %}
                            <span class="text-gray-400">-</span>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                        No requests found matching the current filters.
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
            </div>
            <div class="flex space-x-1">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}" 
                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-md">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}&{{ request.GET.urlencode }}" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}" 
                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
