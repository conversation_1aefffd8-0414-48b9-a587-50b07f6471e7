{% extends 'base.html' %}
{% load static %}

{% block title %}Advanced Request Search{% endblock %}
{% block page_title %}Advanced Request Search{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Advanced Request Search</h1>
            <p class="text-gray-600">Search and filter supply requests with advanced criteria</p>
        </div>
        <div class="flex space-x-2">
            <!-- Save Search Button -->
            <button onclick="showSaveSearchModal()" 
                    class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                </svg>
                Save Search
            </button>
            
            <!-- My Saved Searches -->
            <a href="{% url 'my_saved_searches' %}?type=requests" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                My Searches
            </a>
        </div>
    </div>

    <!-- Advanced Search Form -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Search Filters</h3>
        
        <form hx-get="{% url 'advanced_request_search' %}" 
              hx-target="#search-results" 
              hx-indicator="#search-loading"
              hx-trigger="change, submit, keyup delay:300ms from:input[name='q']"
              class="space-y-4">
            
            <!-- Text Search -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <label for="q" class="block text-sm font-medium text-gray-700 mb-1">Search Query</label>
                    <div class="relative">
                        <input type="text" 
                               name="q" 
                               id="q"
                               value="{{ search_params.q }}"
                               placeholder="Search by request number, requester, department, or items..."
                               class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters Row 1 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select name="status" id="status" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Statuses</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if search_params.status == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Department Filter -->
                <div>
                    <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    <select name="department" id="department" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Departments</option>
                        {% for dept in departments %}
                            <option value="{{ dept }}" {% if search_params.department == dept %}selected{% endif %}>
                                {{ dept }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Requester Filter -->
                <div>
                    <label for="requester" class="block text-sm font-medium text-gray-700 mb-1">Requester</label>
                    <select name="requester" id="requester" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Requesters</option>
                        {% for requester in requesters %}
                            <option value="{{ requester.user.id }}" {% if search_params.requester == requester.user.id|stringformat:"s" %}selected{% endif %}>
                                {{ requester.user.get_full_name|default:requester.user.username }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Has Items Filter -->
                <div>
                    <label for="has_items" class="block text-sm font-medium text-gray-700 mb-1">Items</label>
                    <select name="has_items" id="has_items" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Requests</option>
                        <option value="with_items" {% if search_params.has_items == 'with_items' %}selected{% endif %}>With Items</option>
                        <option value="no_items" {% if search_params.has_items == 'no_items' %}selected{% endif %}>No Items</option>
                    </select>
                </div>
            </div>
            
            <!-- Date Filters -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">Request Date From</label>
                    <input type="date" 
                           name="date_from" 
                           id="date_from"
                           value="{{ search_params.date_from }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">Request Date To</label>
                    <input type="date" 
                           name="date_to" 
                           id="date_to"
                           value="{{ search_params.date_to }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <!-- Sort Options -->
                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select name="sort" id="sort" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="request_date" {% if search_params.sort == 'request_date' %}selected{% endif %}>Request Date (Newest)</option>
                        <option value="request_date_asc" {% if search_params.sort == 'request_date_asc' %}selected{% endif %}>Request Date (Oldest)</option>
                        <option value="request_number" {% if search_params.sort == 'request_number' %}selected{% endif %}>Request Number</option>
                        <option value="requester" {% if search_params.sort == 'requester' %}selected{% endif %}>Requester</option>
                        <option value="department" {% if search_params.sort == 'department' %}selected{% endif %}>Department</option>
                        <option value="status" {% if search_params.sort == 'status' %}selected{% endif %}>Status</option>
                        <option value="item_count" {% if search_params.sort == 'item_count' %}selected{% endif %}>Item Count</option>
                    </select>
                </div>
            </div>
            
            <!-- Clear Filters -->
            <div class="flex justify-end">
                <a href="{% url 'advanced_request_search' %}" 
                   class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                    Clear All Filters
                </a>
            </div>
        </form>
    </div>

    <!-- Loading Indicator -->
    <div id="search-loading" class="htmx-indicator">
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600">Searching...</span>
        </div>
    </div>

    <!-- Search Results -->
    <div id="search-results">
        {% include 'search/partials/request_results.html' %}
    </div>
</div>

<!-- Save Search Modal -->
<div id="save-search-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Save Search</h3>
            <form id="save-search-form">
                <div class="mb-4">
                    <label for="search-name" class="block text-sm font-medium text-gray-700 mb-1">Search Name</label>
                    <input type="text" id="search-name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Enter a name for this search">
                </div>
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="search-public" name="is_public" class="mr-2">
                        <span class="text-sm text-gray-700">Make this search public (visible to other users)</span>
                    </label>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideSaveSearchModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        Save Search
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showSaveSearchModal() {
    document.getElementById('save-search-modal').classList.remove('hidden');
}

function hideSaveSearchModal() {
    document.getElementById('save-search-modal').classList.add('hidden');
}

document.getElementById('save-search-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(document.querySelector('form[hx-get]'));
    const searchParams = {};
    for (let [key, value] of formData.entries()) {
        if (value) searchParams[key] = value;
    }
    
    const saveData = {
        name: document.getElementById('search-name').value,
        search_type: 'requests',
        search_params: searchParams,
        is_public: document.getElementById('search-public').checked
    };
    
    fetch('{% url "save_search" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(saveData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Search saved successfully!');
            hideSaveSearchModal();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error saving search: ' + error);
    });
});
</script>
{% endblock %}
