"""
Views for reporting and analytics functionality
"""
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.db.models import Q, Count, Sum, Avg, F, Case, When, IntegerField
from django.utils import timezone
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import json

from .models import (
    SupplyRequest, RequestItem, SupplyItem, SupplyCategory,
    InventoryTransaction, QRScanLog, UserProfile
)
from .decorators import role_required


@login_required
@role_required(['admin', 'gso_staff'])
def reports_dashboard(request):
    """Main reports dashboard"""
    context = {
        'page_title': 'Reports & Analytics',
        'report_types': [
            {
                'name': 'Request Summary',
                'description': 'Summary of supply requests by date, department, and status',
                'url': 'request_summary_report',
                'icon': 'clipboard-list'
            },
            {
                'name': 'Inventory Report',
                'description': 'Current stock levels, usage trends, and reorder recommendations',
                'url': 'inventory_report',
                'icon': 'cube'
            },
            {
                'name': 'Usage Log Report',
                'description': 'Detailed transaction history and inventory movements',
                'url': 'usage_log_report',
                'icon': 'chart-bar'
            },
            {
                'name': 'Analytics Dashboard',
                'description': 'Real-time statistics and visual analytics',
                'url': 'analytics_dashboard',
                'icon': 'chart-pie'
            }
        ]
    }
    return render(request, 'reports/dashboard.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def request_summary_report(request):
    """Request summary report with filtering"""
    # Get filter parameters
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    department = request.GET.get('department', '')
    status = request.GET.get('status', '')
    
    # Base queryset
    requests = SupplyRequest.objects.select_related('requester', 'approved_by').all()
    
    # Apply filters
    if date_from:
        requests = requests.filter(request_date__date__gte=date_from)
    if date_to:
        requests = requests.filter(request_date__date__lte=date_to)
    if department:
        requests = requests.filter(department__icontains=department)
    if status:
        requests = requests.filter(status=status)
    
    # Get summary statistics
    summary_stats = {
        'total_requests': requests.count(),
        'pending_requests': requests.filter(status='pending').count(),
        'approved_requests': requests.filter(status='approved').count(),
        'rejected_requests': requests.filter(status='rejected').count(),
        'released_requests': requests.filter(status='released').count(),
    }
    
    # Department breakdown
    dept_breakdown = requests.values('department').annotate(
        count=Count('id'),
        pending=Count(Case(When(status='pending', then=1), output_field=IntegerField())),
        approved=Count(Case(When(status='approved', then=1), output_field=IntegerField())),
        rejected=Count(Case(When(status='rejected', then=1), output_field=IntegerField())),
        released=Count(Case(When(status='released', then=1), output_field=IntegerField())),
    ).order_by('-count')
    
    # Status breakdown by date (last 30 days)
    thirty_days_ago = timezone.now() - timedelta(days=30)
    daily_stats = requests.filter(request_date__gte=thirty_days_ago).extra(
        select={'day': 'date(request_date)'}
    ).values('day').annotate(
        total=Count('id'),
        pending=Count(Case(When(status='pending', then=1), output_field=IntegerField())),
        approved=Count(Case(When(status='approved', then=1), output_field=IntegerField())),
        rejected=Count(Case(When(status='rejected', then=1), output_field=IntegerField())),
        released=Count(Case(When(status='released', then=1), output_field=IntegerField())),
    ).order_by('day')
    
    # Pagination for detailed list
    paginator = Paginator(requests.order_by('-request_date'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get unique departments for filter
    departments = SupplyRequest.objects.values_list('department', flat=True).distinct().order_by('department')
    
    context = {
        'page_title': 'Request Summary Report',
        'summary_stats': summary_stats,
        'dept_breakdown': dept_breakdown,
        'daily_stats': list(daily_stats),
        'page_obj': page_obj,
        'departments': departments,
        'status_choices': SupplyRequest.STATUS_CHOICES,
        'filters': {
            'date_from': date_from,
            'date_to': date_to,
            'department': department,
            'status': status,
        }
    }
    
    if request.headers.get('HX-Request'):
        return render(request, 'reports/partials/request_summary_content.html', context)
    
    return render(request, 'reports/request_summary.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def inventory_report(request):
    """Inventory level report with stock analysis"""
    # Get filter parameters
    category_id = request.GET.get('category', '')
    stock_level = request.GET.get('stock_level', '')  # low, normal, out
    
    # Base queryset
    items = SupplyItem.objects.select_related('category').all()
    
    # Apply filters
    if category_id:
        items = items.filter(category_id=category_id)
    
    if stock_level == 'low':
        items = items.filter(current_stock__lte=F('minimum_stock'), current_stock__gt=0)
    elif stock_level == 'out':
        items = items.filter(current_stock=0)
    elif stock_level == 'normal':
        items = items.filter(current_stock__gt=F('minimum_stock'))
    
    # Calculate stock statistics
    stock_stats = {
        'total_items': SupplyItem.objects.count(),
        'low_stock_items': SupplyItem.objects.filter(
            current_stock__lte=F('minimum_stock'), 
            current_stock__gt=0
        ).count(),
        'out_of_stock_items': SupplyItem.objects.filter(current_stock=0).count(),
        'normal_stock_items': SupplyItem.objects.filter(
            current_stock__gt=F('minimum_stock')
        ).count(),
        'total_stock_value': items.aggregate(
            total=Sum(F('current_stock'))
        )['total'] or 0,
    }
    
    # Category breakdown
    category_breakdown = SupplyCategory.objects.annotate(
        total_items=Count('items'),
        low_stock=Count(Case(
            When(items__current_stock__lte=F('items__minimum_stock'), 
                 items__current_stock__gt=0, then=1),
            output_field=IntegerField()
        )),
        out_of_stock=Count(Case(
            When(items__current_stock=0, then=1),
            output_field=IntegerField()
        )),
        total_stock=Sum('items__current_stock'),
    ).order_by('name')
    
    # Recent transactions (last 30 days)
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_transactions = InventoryTransaction.objects.filter(
        transaction_date__gte=thirty_days_ago
    ).select_related('supply_item', 'performed_by').order_by('-transaction_date')[:10]
    
    # Usage trends (items with most transactions)
    usage_trends = SupplyItem.objects.annotate(
        transaction_count=Count('transactions'),
        total_out=Sum(Case(
            When(transactions__transaction_type='out',
                 then='transactions__quantity'),
            output_field=IntegerField(),
            default=0
        )),
        total_in=Sum(Case(
            When(transactions__transaction_type='in',
                 then='transactions__quantity'),
            output_field=IntegerField(),
            default=0
        ))
    ).filter(transaction_count__gt=0).order_by('-transaction_count')[:10]
    
    # Reorder recommendations (low stock items)
    reorder_recommendations = SupplyItem.objects.filter(
        current_stock__lte=F('minimum_stock')
    ).annotate(
        shortage=F('minimum_stock') - F('current_stock'),
        recommended_order=F('minimum_stock') * 2  # Recommend ordering 2x minimum
    ).order_by('current_stock')
    
    # Pagination
    paginator = Paginator(items.order_by('name'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_title': 'Inventory Report',
        'stock_stats': stock_stats,
        'category_breakdown': category_breakdown,
        'recent_transactions': recent_transactions,
        'usage_trends': usage_trends,
        'reorder_recommendations': reorder_recommendations,
        'page_obj': page_obj,
        'categories': SupplyCategory.objects.all().order_by('name'),
        'filters': {
            'category': category_id,
            'stock_level': stock_level,
        }
    }
    
    if request.headers.get('HX-Request'):
        return render(request, 'reports/partials/inventory_content.html', context)

    return render(request, 'reports/inventory.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def usage_log_report(request):
    """Usage log report with transaction history"""
    # Get filter parameters
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    transaction_type = request.GET.get('transaction_type', '')
    item_id = request.GET.get('item_id', '')
    user_id = request.GET.get('user_id', '')

    # Base queryset
    transactions = InventoryTransaction.objects.select_related(
        'supply_item', 'performed_by'
    ).all()

    # Apply filters
    if date_from:
        transactions = transactions.filter(transaction_date__date__gte=date_from)
    if date_to:
        transactions = transactions.filter(transaction_date__date__lte=date_to)
    if transaction_type:
        transactions = transactions.filter(transaction_type=transaction_type)
    if item_id:
        transactions = transactions.filter(supply_item_id=item_id)
    if user_id:
        transactions = transactions.filter(performed_by_id=user_id)

    # Summary statistics
    summary_stats = {
        'total_transactions': transactions.count(),
        'stock_in_transactions': transactions.filter(transaction_type='in').count(),
        'stock_out_transactions': transactions.filter(transaction_type='out').count(),
        'adjustment_transactions': transactions.filter(transaction_type='adjustment').count(),
        'total_quantity_in': transactions.filter(transaction_type='in').aggregate(
            total=Sum('quantity')
        )['total'] or 0,
        'total_quantity_out': transactions.filter(transaction_type='out').aggregate(
            total=Sum('quantity')
        )['total'] or 0,
    }

    # Transaction trends by day (last 30 days)
    thirty_days_ago = timezone.now() - timedelta(days=30)
    daily_transactions = transactions.filter(transaction_date__gte=thirty_days_ago).extra(
        select={'day': 'date(transaction_date)'}
    ).values('day').annotate(
        total=Count('id'),
        stock_in=Count(Case(When(transaction_type='in', then=1), output_field=IntegerField())),
        stock_out=Count(Case(When(transaction_type='out', then=1), output_field=IntegerField())),
        adjustments=Count(Case(When(transaction_type='adjustment', then=1), output_field=IntegerField())),
    ).order_by('day')

    # Most active users
    active_users = transactions.values('performed_by__username', 'performed_by__first_name', 'performed_by__last_name').annotate(
        transaction_count=Count('id'),
        total_quantity=Sum('quantity')
    ).order_by('-transaction_count')[:10]

    # Most transacted items
    active_items = transactions.values('supply_item__name', 'supply_item__id').annotate(
        transaction_count=Count('id'),
        total_quantity_in=Sum(Case(
            When(transaction_type='in', then='quantity'),
            output_field=IntegerField(),
            default=0
        )),
        total_quantity_out=Sum(Case(
            When(transaction_type='out', then='quantity'),
            output_field=IntegerField(),
            default=0
        ))
    ).order_by('-transaction_count')[:10]

    # Pagination
    paginator = Paginator(transactions.order_by('-transaction_date'), 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_title': 'Usage Log Report',
        'summary_stats': summary_stats,
        'daily_transactions': list(daily_transactions),
        'active_users': active_users,
        'active_items': active_items,
        'page_obj': page_obj,
        'transaction_types': InventoryTransaction.TRANSACTION_TYPE_CHOICES,
        'supply_items': SupplyItem.objects.all().order_by('name'),
        'users': UserProfile.objects.select_related('user').all(),
        'filters': {
            'date_from': date_from,
            'date_to': date_to,
            'transaction_type': transaction_type,
            'item_id': item_id,
            'user_id': user_id,
        }
    }

    if request.headers.get('HX-Request'):
        return render(request, 'reports/partials/usage_log_content.html', context)

    return render(request, 'reports/usage_log.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def analytics_dashboard(request):
    """Analytics dashboard with real-time statistics"""
    # Get date range for analytics (default last 30 days)
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)

    # Request analytics
    request_analytics = {
        'total_requests': SupplyRequest.objects.filter(request_date__gte=start_date).count(),
        'pending_requests': SupplyRequest.objects.filter(
            request_date__gte=start_date, status='pending'
        ).count(),
        'approved_requests': SupplyRequest.objects.filter(
            request_date__gte=start_date, status='approved'
        ).count(),
        'rejected_requests': SupplyRequest.objects.filter(
            request_date__gte=start_date, status='rejected'
        ).count(),
        'released_requests': SupplyRequest.objects.filter(
            request_date__gte=start_date, status='released'
        ).count(),
    }

    # Inventory analytics
    inventory_analytics = {
        'total_items': SupplyItem.objects.count(),
        'low_stock_items': SupplyItem.objects.filter(
            current_stock__lte=F('minimum_stock'), current_stock__gt=0
        ).count(),
        'out_of_stock_items': SupplyItem.objects.filter(current_stock=0).count(),
        'total_transactions': InventoryTransaction.objects.filter(
            transaction_date__gte=start_date
        ).count(),
    }

    # QR scan analytics
    scan_analytics = {
        'total_scans': QRScanLog.objects.filter(scan_datetime__gte=start_date).count(),
        'inventory_scans': QRScanLog.objects.filter(
            scan_datetime__gte=start_date, scan_type='inventory_check'
        ).count(),
        'issuance_scans': QRScanLog.objects.filter(
            scan_datetime__gte=start_date, scan_type='issuance'
        ).count(),
        'return_scans': QRScanLog.objects.filter(
            scan_datetime__gte=start_date, scan_type='return'
        ).count(),
    }

    # Department activity
    dept_activity = SupplyRequest.objects.filter(
        request_date__gte=start_date
    ).values('department').annotate(
        request_count=Count('id'),
        avg_approval_time=Avg(
            Case(
                When(approved_date__isnull=False,
                     then=F('approved_date') - F('request_date')),
                output_field=IntegerField()
            )
        )
    ).order_by('-request_count')[:10]

    context = {
        'page_title': 'Analytics Dashboard',
        'request_analytics': request_analytics,
        'inventory_analytics': inventory_analytics,
        'scan_analytics': scan_analytics,
        'dept_activity': dept_activity,
        'days': days,
        'start_date': start_date,
    }

    if request.headers.get('HX-Request'):
        return render(request, 'reports/partials/analytics_content.html', context)

    return render(request, 'reports/analytics.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def export_report(request, report_type):
    """Export reports to PDF or Excel"""
    export_format = request.GET.get('format', 'pdf')  # pdf or excel

    if report_type == 'request_summary':
        return export_request_summary(request, export_format)
    elif report_type == 'inventory':
        return export_inventory_report(request, export_format)
    elif report_type == 'usage_log':
        return export_usage_log(request, export_format)
    else:
        return JsonResponse({'error': 'Invalid report type'}, status=400)


def export_request_summary(request, export_format):
    """Export request summary report"""
    from django.template.loader import get_template
    from django.http import HttpResponse
    import csv
    from io import StringIO

    # Get the same data as the report view
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    department = request.GET.get('department', '')
    status = request.GET.get('status', '')

    requests = SupplyRequest.objects.select_related('requester', 'approved_by').all()

    if date_from:
        requests = requests.filter(request_date__date__gte=date_from)
    if date_to:
        requests = requests.filter(request_date__date__lte=date_to)
    if department:
        requests = requests.filter(department__icontains=department)
    if status:
        requests = requests.filter(status=status)

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="request_summary_{timezone.now().strftime("%Y%m%d")}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Request Number', 'Requester', 'Department', 'Status',
            'Request Date', 'Approved Date', 'Approved By'
        ])

        for req in requests:
            writer.writerow([
                req.request_number,
                req.requester.get_full_name() or req.requester.username,
                req.department,
                req.get_status_display(),
                req.request_date.strftime('%Y-%m-%d %H:%M'),
                req.approved_date.strftime('%Y-%m-%d %H:%M') if req.approved_date else '',
                req.approved_by.get_full_name() if req.approved_by else ''
            ])

        return response

    # For PDF export (placeholder - would need reportlab or weasyprint)
    return JsonResponse({'message': 'PDF export not yet implemented'})


def export_inventory_report(request, export_format):
    """Export inventory report"""
    import csv
    from django.http import HttpResponse

    category_id = request.GET.get('category', '')
    stock_level = request.GET.get('stock_level', '')

    items = SupplyItem.objects.select_related('category').all()

    if category_id:
        items = items.filter(category_id=category_id)

    if stock_level == 'low':
        items = items.filter(current_stock__lte=F('minimum_stock'), current_stock__gt=0)
    elif stock_level == 'out':
        items = items.filter(current_stock=0)
    elif stock_level == 'normal':
        items = items.filter(current_stock__gt=F('minimum_stock'))

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="inventory_report_{timezone.now().strftime("%Y%m%d")}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Item Name', 'Category', 'Current Stock', 'Minimum Stock',
            'Unit of Measure', 'Status', 'QR Code Data'
        ])

        for item in items:
            status = 'Out of Stock' if item.current_stock == 0 else (
                'Low Stock' if item.current_stock <= item.minimum_stock else 'Normal'
            )

            writer.writerow([
                item.name,
                item.category.name,
                item.current_stock,
                item.minimum_stock,
                item.unit_of_measure,
                status,
                item.qr_code_data
            ])

        return response

    return JsonResponse({'message': 'PDF export not yet implemented'})


def export_usage_log(request, export_format):
    """Export usage log report"""
    import csv
    from django.http import HttpResponse

    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    transaction_type = request.GET.get('transaction_type', '')

    transactions = InventoryTransaction.objects.select_related(
        'supply_item', 'performed_by'
    ).all()

    if date_from:
        transactions = transactions.filter(transaction_date__date__gte=date_from)
    if date_to:
        transactions = transactions.filter(transaction_date__date__lte=date_to)
    if transaction_type:
        transactions = transactions.filter(transaction_type=transaction_type)

    if export_format == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="usage_log_{timezone.now().strftime("%Y%m%d")}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Date', 'Item Name', 'Transaction Type', 'Quantity',
            'Previous Stock', 'New Stock', 'Performed By', 'Reference Number', 'Notes'
        ])

        for transaction in transactions:
            writer.writerow([
                transaction.transaction_date.strftime('%Y-%m-%d %H:%M'),
                transaction.supply_item.name,
                transaction.get_transaction_type_display(),
                transaction.quantity,
                transaction.previous_stock,
                transaction.new_stock,
                transaction.performed_by.get_full_name() or transaction.performed_by.username,
                transaction.reference_number or '',
                transaction.notes or ''
            ])

        return response

    return JsonResponse({'message': 'PDF export not yet implemented'})
