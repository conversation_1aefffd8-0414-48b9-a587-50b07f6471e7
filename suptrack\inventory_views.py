from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, F
from django.core.paginator import Paginator
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import json

from .models import SupplyItem, SupplyCategory, InventoryTransaction
from .forms import SupplyItemForm, SupplyItemSearchForm, InventoryTransactionForm
from .decorators import role_required
from .qr_utils import generate_qr_code_for_item


@login_required
@role_required(['admin', 'gso_staff'])
def inventory_list(request):
    """Display inventory listing with search, filtering, and stock level indicators"""
    
    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')
    stock_filter = request.GET.get('stock_status', '')
    sort_by = request.GET.get('sort', 'name')
    
    # Base queryset
    items = SupplyItem.objects.select_related('category').all()
    
    # Apply search filter
    if search_query:
        items = items.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(category__name__icontains=search_query)
        )
    
    # Apply category filter
    if category_filter:
        items = items.filter(category_id=category_filter)
    
    # Apply stock status filter
    if stock_filter == 'low':
        items = items.filter(current_stock__lte=F('minimum_stock'))
    elif stock_filter == 'out':
        items = items.filter(current_stock=0)
    elif stock_filter == 'normal':
        items = items.filter(current_stock__gt=F('minimum_stock'))
    
    # Apply sorting
    if sort_by == 'stock_asc':
        items = items.order_by('current_stock')
    elif sort_by == 'stock_desc':
        items = items.order_by('-current_stock')
    elif sort_by == 'category':
        items = items.order_by('category__name', 'name')
    elif sort_by == 'updated':
        items = items.order_by('-updated_at')
    else:  # default to name
        items = items.order_by('name')
    
    # Pagination
    paginator = Paginator(items, 20)  # Show 20 items per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get categories for filter dropdown
    categories = SupplyCategory.objects.all().order_by('name')
    
    # Calculate stock statistics
    total_items = SupplyItem.objects.count()
    low_stock_count = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).count()
    out_of_stock_count = SupplyItem.objects.filter(current_stock=0).count()
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'search_query': search_query,
        'category_filter': category_filter,
        'stock_filter': stock_filter,
        'sort_by': sort_by,
        'total_items': total_items,
        'low_stock_count': low_stock_count,
        'out_of_stock_count': out_of_stock_count,
    }
    
    return render(request, 'inventory/inventory_list.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def inventory_list_htmx(request):
    """HTMX endpoint for updating inventory list without page reload"""
    
    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')
    stock_filter = request.GET.get('stock_status', '')
    sort_by = request.GET.get('sort', 'name')
    
    # Base queryset
    items = SupplyItem.objects.select_related('category').all()
    
    # Apply filters (same logic as inventory_list)
    if search_query:
        items = items.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(category__name__icontains=search_query)
        )
    
    if category_filter:
        items = items.filter(category_id=category_filter)
    
    if stock_filter == 'low':
        items = items.filter(current_stock__lte=F('minimum_stock'))
    elif stock_filter == 'out':
        items = items.filter(current_stock=0)
    elif stock_filter == 'normal':
        items = items.filter(current_stock__gt=F('minimum_stock'))
    
    # Apply sorting
    if sort_by == 'stock_asc':
        items = items.order_by('current_stock')
    elif sort_by == 'stock_desc':
        items = items.order_by('-current_stock')
    elif sort_by == 'category':
        items = items.order_by('category__name', 'name')
    elif sort_by == 'updated':
        items = items.order_by('-updated_at')
    else:
        items = items.order_by('name')
    
    # Pagination
    paginator = Paginator(items, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'category_filter': category_filter,
        'stock_filter': stock_filter,
        'sort_by': sort_by,
    }
    
    return render(request, 'inventory/partials/inventory_table.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def add_supply_item(request):
    """Add new supply item"""
    
    if request.method == 'POST':
        form = SupplyItemForm(request.POST, request.FILES)
        if form.is_valid():
            supply_item = form.save()
            
            # Generate QR code for new item
            try:
                generate_qr_code_for_item(supply_item, include_label=True)
                messages.success(request, f'Supply item "{supply_item.name}" has been added successfully with QR code.')
            except Exception as e:
                messages.warning(request, f'Supply item "{supply_item.name}" has been added, but QR code generation failed: {str(e)}')
            
            # Log initial stock transaction if stock > 0
            if supply_item.current_stock > 0:
                InventoryTransaction.objects.create(
                    supply_item=supply_item,
                    transaction_type='in',
                    quantity=supply_item.current_stock,
                    previous_stock=0,
                    new_stock=supply_item.current_stock,
                    performed_by=request.user,
                    notes=f'Initial stock for new item: {supply_item.name}'
                )
            
            return redirect('inventory_list')
    else:
        form = SupplyItemForm()
    
    categories = SupplyCategory.objects.all().order_by('name')
    
    context = {
        'form': form,
        'categories': categories,
        'title': 'Add Supply Item',
        'submit_text': 'Add Item'
    }
    
    return render(request, 'inventory/supply_item_form.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def edit_supply_item(request, pk):
    """Edit existing supply item"""
    
    supply_item = get_object_or_404(SupplyItem, pk=pk)
    original_stock = supply_item.current_stock
    
    if request.method == 'POST':
        form = SupplyItemForm(request.POST, request.FILES, instance=supply_item)
        if form.is_valid():
            updated_item = form.save()
            
            # Log stock adjustment if stock changed
            new_stock = updated_item.current_stock
            if new_stock != original_stock:
                quantity_change = new_stock - original_stock
                transaction_type = 'in' if quantity_change > 0 else 'out'
                
                InventoryTransaction.objects.create(
                    supply_item=updated_item,
                    transaction_type='adjustment',
                    quantity=abs(quantity_change),
                    previous_stock=original_stock,
                    new_stock=new_stock,
                    performed_by=request.user,
                    notes=f'Stock adjustment: {quantity_change:+d} units'
                )
            
            messages.success(request, f'Supply item "{updated_item.name}" has been updated successfully.')
            return redirect('inventory_list')
    else:
        form = SupplyItemForm(instance=supply_item)
    
    categories = SupplyCategory.objects.all().order_by('name')
    
    context = {
        'form': form,
        'categories': categories,
        'supply_item': supply_item,
        'title': 'Edit Supply Item',
        'submit_text': 'Update Item'
    }
    
    return render(request, 'inventory/supply_item_form.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def supply_item_detail(request, pk):
    """Display supply item details with transaction history"""
    
    supply_item = get_object_or_404(SupplyItem, pk=pk)
    
    # Generate QR code if it doesn't exist
    if not supply_item.qr_code:
        try:
            generate_qr_code_for_item(supply_item, include_label=True)
        except Exception as e:
            messages.warning(request, f'Could not generate QR code: {str(e)}')
    
    # Get recent transactions
    transactions = supply_item.transactions.all().order_by('-transaction_date')[:20]
    
    # Get recent scan logs
    scan_logs = supply_item.scan_logs.all().order_by('-scan_datetime')[:10]
    
    context = {
        'supply_item': supply_item,
        'transactions': transactions,
        'scan_logs': scan_logs,
    }
    
    return render(request, 'inventory/supply_item_detail.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
@require_http_methods(["DELETE"])
def delete_supply_item(request, pk):
    """Delete supply item with HTMX confirmation"""
    
    supply_item = get_object_or_404(SupplyItem, pk=pk)
    
    try:
        item_name = supply_item.name
        supply_item.delete()
        
        return JsonResponse({
            'success': True,
            'message': f'Supply item "{item_name}" has been deleted successfully.'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error deleting supply item: {str(e)}'
        }, status=400)


@login_required
@role_required(['admin', 'gso_staff'])
def delete_confirmation_modal(request, pk):
    """HTMX modal for delete confirmation"""
    
    supply_item = get_object_or_404(SupplyItem, pk=pk)
    
    context = {
        'supply_item': supply_item,
    }
    
    return render(request, 'inventory/partials/delete_confirmation_modal.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def stock_adjustment(request, pk):
    """Adjust stock levels for a supply item"""
    
    supply_item = get_object_or_404(SupplyItem, pk=pk)
    
    if request.method == 'POST':
        form = InventoryTransactionForm(request.POST)
        if form.is_valid():
            transaction_type = form.cleaned_data['transaction_type']
            quantity = form.cleaned_data['quantity']
            notes = form.cleaned_data['notes']
            
            previous_stock = supply_item.current_stock
            
            if transaction_type == 'in':
                new_stock = previous_stock + quantity
            elif transaction_type == 'out':
                new_stock = max(0, previous_stock - quantity)
            else:  # adjustment
                new_stock = quantity
            
            # Update supply item stock
            supply_item.current_stock = new_stock
            supply_item.save()
            
            # Create transaction record
            InventoryTransaction.objects.create(
                supply_item=supply_item,
                transaction_type=transaction_type,
                quantity=quantity,
                previous_stock=previous_stock,
                new_stock=new_stock,
                performed_by=request.user,
                notes=notes
            )
            
            messages.success(request, f'Stock adjustment completed for "{supply_item.name}".')
            return redirect('supply_item_detail', pk=pk)
    else:
        form = InventoryTransactionForm()
    
    context = {
        'form': form,
        'supply_item': supply_item,
    }
    
    return render(request, 'inventory/stock_adjustment.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def low_stock_alerts(request):
    """Display items with low stock levels"""
    
    low_stock_items = SupplyItem.objects.filter(
        current_stock__lte=F('minimum_stock')
    ).order_by('current_stock')
    
    out_of_stock_items = low_stock_items.filter(current_stock=0)
    critical_stock_items = low_stock_items.exclude(current_stock=0)
    
    context = {
        'low_stock_items': low_stock_items,
        'out_of_stock_items': out_of_stock_items,
        'critical_stock_items': critical_stock_items,
    }
    
    return render(request, 'inventory/low_stock_alerts.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def inventory_transactions(request):
    """Display inventory transaction history"""
    
    transactions = InventoryTransaction.objects.select_related(
        'supply_item', 'performed_by'
    ).all().order_by('-transaction_date')
    
    # Filter by supply item if specified
    item_filter = request.GET.get('item')
    if item_filter:
        transactions = transactions.filter(supply_item_id=item_filter)
    
    # Filter by transaction type if specified
    type_filter = request.GET.get('type')
    if type_filter:
        transactions = transactions.filter(transaction_type=type_filter)
    
    # Pagination
    paginator = Paginator(transactions, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get supply items for filter dropdown
    supply_items = SupplyItem.objects.all().order_by('name')
    
    context = {
        'page_obj': page_obj,
        'supply_items': supply_items,
        'item_filter': item_filter,
        'type_filter': type_filter,
        'transaction_types': InventoryTransaction.TRANSACTION_TYPE_CHOICES,
    }
    
    return render(request, 'inventory/inventory_transactions.html', context)