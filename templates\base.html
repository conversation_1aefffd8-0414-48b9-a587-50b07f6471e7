<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Smart Supply Management System{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.7.2/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.7.2/unpoly.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- jsQR for QR code scanning -->
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
    
    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    <style>
        /* Custom styles for mobile-first responsive design */
        .sidebar-collapsed {
            transform: translateX(-100%);
        }
        
        .sidebar-expanded {
            transform: translateX(0);
        }
        
        @media (min-width: 768px) {
            .sidebar-collapsed {
                transform: translateX(0);
            }
        }
        
        /* Loading states */
        .htmx-request {
            opacity: 0.6;
        }
        
        .htmx-indicator {
            display: none;
        }
        
        .htmx-request .htmx-indicator {
            display: inline;
        }
        
        /* QR Scanner styles */
        #qr-video {
            width: 100%;
            max-width: 400px;
            height: auto;
        }
        
        .qr-scanner-overlay {
            position: relative;
        }
        
        .qr-scanner-overlay::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            height: 200px;
            border: 2px solid #10b981;
            border-radius: 8px;
            transform: translate(-50%, -50%);
            pointer-events: none;
        }

        /* GSO Navigation Styles */
        .nav-link.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            border-right: 2px solid #3b82f6;
        }

        .nav-sublink.active {
            background-color: #f3f4f6;
            color: #374151;
            font-weight: 500;
        }

        .nav-submenu {
            transition: all 0.3s ease-in-out;
            max-height: 0;
            overflow: hidden;
        }

        .nav-submenu:not(.hidden) {
            max-height: 500px;
        }

        .nav-section .nav-link svg:last-child {
            transition: transform 0.2s ease-in-out;
        }

        .nav-section .nav-link svg:last-child.rotate-180 {
            transform: rotate(180deg);
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans antialiased" x-data="{ sidebarOpen: false }">
    <!-- Mobile menu overlay -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 md:hidden"
         @click="sidebarOpen = false">
    </div>

    <!-- Sidebar (only show for authenticated users) -->
    {% if user.is_authenticated %}
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0"
         :class="sidebarOpen ? 'translate-x-0' : '-translate-x-full'"
         @click.away="sidebarOpen = false">
        
        <!-- Sidebar header -->
        <div class="flex items-center justify-between h-16 px-4 bg-blue-600 text-white">
            <h1 class="text-lg font-semibold">Smart Supply</h1>
            <button @click="sidebarOpen = false" class="md:hidden">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Sidebar navigation -->
        <nav class="mt-8">
            {% block sidebar_nav %}
            <div class="px-4 space-y-2">
                <a href="{% url 'dashboard' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    Dashboard
                </a>
                
                {% if user.userprofile.role == 'admin' or user.userprofile.role == 'gso_staff' %}
                <a href="#" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Inventory
                </a>
                {% endif %}
                
                <a href="#" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Requests
                </a>
                
                {% if user.userprofile.role == 'gso_staff' %}
                <a href="#" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                    </svg>
                    QR Scanner
                </a>
                {% endif %}
                
                {% if user.userprofile.role == 'admin' or user.userprofile.role == 'gso_staff' %}
                <a href="{% url 'reports_dashboard' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z"></path>
                    </svg>
                    Reports
                </a>
                {% endif %}
            </div>
            {% endblock %}
        </nav>
        
        <!-- User info -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        {{ user.first_name.0|default:user.username.0|upper }}
                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-700">{{ user.get_full_name|default:user.username }}</p>
                    <p class="text-xs text-gray-500">{{ user.userprofile.get_role_display|default:"User" }}</p>
                </div>
            </div>
            <a href="{% url 'logout' %}" class="mt-2 w-full flex items-center justify-center px-4 py-2 text-sm text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200">
                Logout
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Main content -->
    <div class="{% if user.is_authenticated %}md:ml-64{% endif %}">
        <!-- Top navigation bar (only for authenticated users) -->
        {% if user.is_authenticated %}
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <!-- Mobile menu button -->
                        <button @click="sidebarOpen = !sidebarOpen"
                                class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                :aria-expanded="sidebarOpen"
                                aria-label="Toggle navigation menu">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>

                        <!-- Page title -->
                        <h2 class="ml-2 text-lg font-semibold text-gray-900">
                            {% block page_title %}Dashboard{% endblock %}
                        </h2>
                    </div>

                    <!-- Top navigation actions -->
                    <div class="flex items-center space-x-4">
                        {% block top_nav_actions %}
                        <!-- Enhanced Notifications -->
                        <div class="relative" x-data="{ showNotifications: false }">
                            <button @click="showNotifications = !showNotifications"
                                    class="p-2 text-gray-400 hover:text-gray-500 relative">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z"></path>
                                </svg>
                                <!-- Dynamic notification badge -->
                                <div hx-get="{% url 'live_notification_count' %}"
                                     hx-trigger="load, every 30s"
                                     hx-swap="innerHTML"
                                     class="absolute -top-1 -right-1">
                                    <!-- Badge will load here -->
                                </div>
                            </button>

                            <!-- Enhanced notifications dropdown -->
                            <div x-show="showNotifications"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 @click.away="showNotifications = false"
                                 class="absolute right-0 mt-2 w-96 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                                <div class="p-4">
                                    <div class="flex items-center justify-between mb-3">
                                        <h3 class="text-sm font-medium text-gray-900">Notifications</h3>
                                        <a href="{% url 'notification_center' %}"
                                           class="text-xs text-blue-600 hover:text-blue-500 font-medium">
                                            View All
                                        </a>
                                    </div>
                                    <div hx-get="{% url 'notification_dropdown' %}"
                                         hx-trigger="load, every 30s"
                                         hx-swap="innerHTML">
                                        <!-- Notifications will load here -->
                                        <div class="text-center py-4">
                                            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                                            <p class="text-sm text-gray-500 mt-2">Loading notifications...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endblock %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Page content -->
        <main class="{% if user.is_authenticated %}p-4 sm:p-6 lg:p-8{% endif %}">
            <!-- Breadcrumbs -->
            {% if user.is_authenticated %}
                {% include 'partials/breadcrumbs.html' %}
            {% endif %}

            <!-- Messages -->
            {% if messages %}
            <div class="mb-4">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} p-4 mb-4 text-sm rounded-lg {% if message.tags == 'error' %}bg-red-50 text-red-800 border border-red-200{% elif message.tags == 'success' %}bg-green-50 text-green-800 border border-green-200{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-800 border border-yellow-200{% else %}bg-blue-50 text-blue-800 border border-blue-200{% endif %}"
                     x-data="{ show: true }"
                     x-show="show"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform scale-90"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-300"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-90">
                    <div class="flex justify-between items-center">
                        <span>{{ message }}</span>
                        <button @click="show = false" class="ml-4 text-current opacity-50 hover:opacity-75">
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Main content area -->
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="htmx-indicator fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg">
        <div class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        </div>
    </div>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/app.js' %}"></script>
    
    {% block extra_scripts %}{% endblock %}
    
    <!-- HTMX Configuration -->
    <script>
        // Configure HTMX
        document.body.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = document.querySelector('[name=csrfmiddlewaretoken]').value;
        });
        
        // Handle HTMX errors
        document.body.addEventListener('htmx:responseError', function(evt) {
            console.error('HTMX Error:', evt.detail);
            // Show error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            errorDiv.textContent = 'An error occurred. Please try again.';
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        });
    </script>
</body>
</html>