<!-- Results Header -->
<div class="bg-white rounded-lg shadow-md mb-4">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">
                Search Results
                {% if total_results %}
                    <span class="text-sm font-normal text-gray-600">({{ total_results }} item{{ total_results|pluralize }})</span>
                {% endif %}
            </h3>
            
            {% if page_obj.has_other_pages %}
                <div class="text-sm text-gray-600">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Results Grid -->
    <div class="p-6">
        {% if page_obj.object_list %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for item in page_obj.object_list %}
                    <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                        <!-- Item Header -->
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-1">
                                    <a href="{% url 'supply_item_detail' item.id %}" 
                                       class="text-blue-600 hover:text-blue-800">
                                        {{ item.name }}
                                    </a>
                                </h4>
                                <p class="text-sm text-gray-600">{{ item.category.name }}</p>
                            </div>
                            
                            <!-- Stock Status Badge -->
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                {% if item.current_stock == 0 %}bg-red-100 text-red-800
                                {% elif item.current_stock <= item.minimum_stock %}bg-yellow-100 text-yellow-800
                                {% else %}bg-green-100 text-green-800{% endif %}">
                                {% if item.current_stock == 0 %}Out of Stock
                                {% elif item.current_stock <= item.minimum_stock %}Low Stock
                                {% else %}Normal{% endif %}
                            </span>
                        </div>
                        
                        <!-- Item Details -->
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Current Stock:</span>
                                <span class="font-medium">{{ item.current_stock }} {{ item.unit_of_measure }}</span>
                            </div>
                            
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Minimum Stock:</span>
                                <span class="font-medium">{{ item.minimum_stock }} {{ item.unit_of_measure }}</span>
                            </div>
                            
                            {% if item.total_requests %}
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Total Requests:</span>
                                    <span class="font-medium">{{ item.total_requests }}</span>
                                </div>
                            {% endif %}
                            
                            {% if item.description %}
                                <div class="mt-2">
                                    <p class="text-xs text-gray-600">{{ item.description|truncatechars:100 }}</p>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="mt-4 flex space-x-2">
                            <a href="{% url 'supply_item_detail' item.id %}" 
                               class="flex-1 text-center px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                View Details
                            </a>
                            
                            {% if user.userprofile.role == 'admin' or user.userprofile.role == 'gso_staff' %}
                                <button hx-get="{% url 'inline_edit_supply_item' item.id %}"
                                        hx-target="#supply-item-{{ item.id }}"
                                        hx-swap="outerHTML"
                                        class="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors">
                                    Edit
                                </button>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- No Results -->
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No items found</h3>
                <p class="text-gray-600 mb-4">Try adjusting your search criteria or clearing some filters.</p>
                <a href="{% url 'advanced_inventory_search' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    Clear All Filters
                </a>
            </div>
        {% endif %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                </div>
                
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                        <button hx-get="{% url 'advanced_inventory_search' %}?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}"
                                hx-target="#search-results"
                                hx-indicator="#search-loading"
                                class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Previous
                        </button>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-md">{{ num }}</span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <button hx-get="{% url 'advanced_inventory_search' %}?page={{ num }}&{{ request.GET.urlencode }}"
                                    hx-target="#search-results"
                                    hx-indicator="#search-loading"
                                    class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                {{ num }}
                            </button>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <button hx-get="{% url 'advanced_inventory_search' %}?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}"
                                hx-target="#search-results"
                                hx-indicator="#search-loading"
                                class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Next
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
</div>
