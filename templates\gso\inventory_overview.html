{% extends 'base.html' %}

{% block title %}Inventory Overview - GSO Dashboard{% endblock %}

{% block page_title %}Inventory Overview{% endblock %}

{% block sidebar_nav %}
    {% include 'partials/gso_sidebar_nav.html' %}
{% endblock %}

{% block content %}
<!-- Inventory Statistics -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Items</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ total_items }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Low Stock</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ low_stock_items }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Out of Stock</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ out_of_stock_items }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-white shadow rounded-lg mb-8">
    <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <a href="{% url 'gso_inventory_list' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                </svg>
                View All Items
            </a>
            <a href="{% url 'gso_inventory_add' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add New Item
            </a>
            <a href="{% url 'gso_stock_adjustment' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 110 2h-1v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6H3a1 1 0 110-2h4z"></path>
                </svg>
                Stock Adjustment
            </a>
            <a href="{% url 'gso_low_stock' %}" 
               class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                Low Stock Alerts
            </a>
        </div>
    </div>
</div>

<!-- Items Needing Attention -->
{% if attention_items %}
<div class="bg-white shadow rounded-lg mb-8">
    <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Items Needing Attention</h3>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stock</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Minimum Stock</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for item in attention_items %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                                    <div class="text-sm text-gray-500">{{ item.category.name }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.current_stock }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.minimum_stock }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if item.current_stock == 0 %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Out of Stock</span>
                            {% elif item.current_stock <= item.minimum_stock %}
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Low Stock</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'gso_inventory_detail' item.pk %}" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                            <a href="{% url 'gso_inventory_edit' item.pk %}" class="text-green-600 hover:text-green-900">Edit</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Transactions -->
{% if recent_transactions %}
<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Transactions</h3>
            <a href="{% url 'gso_inventory_transactions' %}" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
        </div>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for transaction in recent_transactions %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ transaction.transaction_date|date:"M d, Y H:i" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ transaction.supply_item.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                {% if transaction.transaction_type == 'in' %}bg-green-100 text-green-800
                                {% elif transaction.transaction_type == 'out' %}bg-red-100 text-red-800
                                {% else %}bg-blue-100 text-blue-800{% endif %}">
                                {{ transaction.get_transaction_type_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if transaction.transaction_type == 'out' %}-{% endif %}{{ transaction.quantity }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ transaction.user.get_full_name|default:transaction.user.username }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize GSO navigation
        initGSONavigation();
    });
    
    // Include the same navigation functions from the main dashboard
    function initGSONavigation() {
        setActiveNavItem();
        initSubmenuToggles();
        initNavStatePersistence();
    }
    
    function setActiveNavItem() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link, .nav-sublink');
        
        navLinks.forEach(link => {
            link.classList.remove('bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
            link.classList.add('text-gray-700');
        });
        
        let activeLink = null;
        let activeSection = null;
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.startsWith(href) && href !== '/gso/') {
                if (!activeLink || href.length > activeLink.getAttribute('href').length) {
                    activeLink = link;
                    activeSection = link.closest('.nav-section');
                }
            }
        });
        
        if (activeLink) {
            activeLink.classList.remove('text-gray-700');
            activeLink.classList.add('bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
            
            if (activeLink.classList.contains('nav-sublink') && activeSection) {
                const parentLink = activeSection.querySelector('.nav-link');
                if (parentLink) {
                    parentLink.classList.remove('text-gray-700');
                    parentLink.classList.add('bg-blue-50', 'text-blue-600');
                    
                    const submenu = activeSection.querySelector('.nav-submenu');
                    if (submenu) {
                        submenu.classList.remove('hidden');
                        const arrow = parentLink.querySelector('svg:last-child');
                        if (arrow) {
                            arrow.classList.add('rotate-180');
                        }
                    }
                }
            }
        }
    }
    
    function initSubmenuToggles() {
        const navSections = document.querySelectorAll('.nav-section');
        
        navSections.forEach(section => {
            const mainLink = section.querySelector('.nav-link');
            const submenu = section.querySelector('.nav-submenu');
            const arrow = mainLink?.querySelector('svg:last-child');
            
            if (mainLink && submenu) {
                mainLink.addEventListener('click', function(e) {
                    const isArrowClick = e.target.closest('svg:last-child');
                    const isSubmenuHidden = submenu.classList.contains('hidden');
                    
                    if (isArrowClick || isSubmenuHidden) {
                        e.preventDefault();
                        submenu.classList.toggle('hidden');
                        
                        if (arrow) {
                            arrow.classList.toggle('rotate-180');
                        }
                        
                        const sectionName = section.getAttribute('data-nav-section');
                        if (sectionName) {
                            localStorage.setItem(`gso-nav-${sectionName}`, !submenu.classList.contains('hidden'));
                        }
                    }
                });
            }
        });
    }
    
    function initNavStatePersistence() {
        const navSections = document.querySelectorAll('.nav-section');
        
        navSections.forEach(section => {
            const sectionName = section.getAttribute('data-nav-section');
            if (sectionName) {
                const isOpen = localStorage.getItem(`gso-nav-${sectionName}`) === 'true';
                const submenu = section.querySelector('.nav-submenu');
                const arrow = section.querySelector('.nav-link svg:last-child');
                
                if (isOpen && submenu) {
                    submenu.classList.remove('hidden');
                    if (arrow) {
                        arrow.classList.add('rotate-180');
                    }
                }
            }
        });
    }
</script>
{% endblock %}
