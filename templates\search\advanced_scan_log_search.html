{% extends 'base.html' %}
{% load static %}

{% block title %}Advanced Scan Log Search{% endblock %}
{% block page_title %}Advanced Scan Log Search{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Advanced Scan Log Search</h1>
            <p class="text-gray-600">Search and filter QR scan logs with advanced criteria</p>
        </div>
    </div>

    <!-- Advanced Search Form -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Search Filters</h3>
        
        <form hx-get="{% url 'advanced_scan_log_search' %}" 
              hx-target="#search-results" 
              hx-indicator="#search-loading"
              hx-trigger="change, submit, keyup delay:300ms from:input[name='q']"
              class="space-y-4">
            
            <!-- Text Search -->
            <div>
                <label for="q" class="block text-sm font-medium text-gray-700 mb-1">Search Query</label>
                <div class="relative">
                    <input type="text" 
                           name="q" 
                           id="q"
                           value="{{ search_params.q }}"
                           placeholder="Search by item, location, notes, or user..."
                           class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Scan Type Filter -->
                <div>
                    <label for="scan_type" class="block text-sm font-medium text-gray-700 mb-1">Scan Type</label>
                    <select name="scan_type" id="scan_type" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Types</option>
                        {% for value, label in scan_type_choices %}
                            <option value="{{ value }}" {% if search_params.scan_type == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- User Filter -->
                <div>
                    <label for="user" class="block text-sm font-medium text-gray-700 mb-1">User</label>
                    <select name="user" id="user" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Users</option>
                        {% for user_profile in users %}
                            <option value="{{ user_profile.user.id }}" {% if search_params.user == user_profile.user.id|stringformat:"s" %}selected{% endif %}>
                                {{ user_profile.user.get_full_name|default:user_profile.user.username }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Location Filter -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                    <input type="text" 
                           name="location" 
                           id="location"
                           value="{{ search_params.location }}"
                           placeholder="Filter by location..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
            
            <!-- Date and Time Filters -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                    <input type="date" 
                           name="date_from" 
                           id="date_from"
                           value="{{ search_params.date_from }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                    <input type="date" 
                           name="date_to" 
                           id="date_to"
                           value="{{ search_params.date_to }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="time_from" class="block text-sm font-medium text-gray-700 mb-1">Time From</label>
                    <input type="time" 
                           name="time_from" 
                           id="time_from"
                           value="{{ search_params.time_from }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="time_to" class="block text-sm font-medium text-gray-700 mb-1">Time To</label>
                    <input type="time" 
                           name="time_to" 
                           id="time_to"
                           value="{{ search_params.time_to }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
            
            <!-- Sort and Clear -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select name="sort" id="sort" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="scan_datetime" {% if search_params.sort == 'scan_datetime' %}selected{% endif %}>Scan Date (Newest)</option>
                        <option value="scan_datetime_asc" {% if search_params.sort == 'scan_datetime_asc' %}selected{% endif %}>Scan Date (Oldest)</option>
                        <option value="user" {% if search_params.sort == 'user' %}selected{% endif %}>User</option>
                        <option value="item" {% if search_params.sort == 'item' %}selected{% endif %}>Item</option>
                        <option value="location" {% if search_params.sort == 'location' %}selected{% endif %}>Location</option>
                        <option value="scan_type" {% if search_params.sort == 'scan_type' %}selected{% endif %}>Scan Type</option>
                    </select>
                </div>
                
                <div class="flex items-end">
                    <a href="{% url 'advanced_scan_log_search' %}" 
                       class="w-full text-center px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        Clear All Filters
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Loading Indicator -->
    <div id="search-loading" class="htmx-indicator">
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600">Searching...</span>
        </div>
    </div>

    <!-- Search Results -->
    <div id="search-results">
        {% include 'search/partials/scan_log_results.html' %}
    </div>
</div>
{% endblock %}
