"""
Management command for restoring system data from backup
"""
import os
import json
import zipfile
from django.core.management.base import BaseCommand
from django.core import serializers
from django.db import transaction
from django.conf import settings


class Command(BaseCommand):
    help = 'Restore system data from backup'
    
    def add_arguments(self, parser):
        parser.add_argument(
            'backup_file',
            type=str,
            help='Path to backup file to restore'
        )
        parser.add_argument(
            '--restore-media',
            action='store_true',
            help='Restore media files from backup'
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing data before restore (DANGEROUS!)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be restored without actually doing it'
        )
    
    def handle(self, *args, **options):
        backup_file = options['backup_file']
        restore_media = options['restore_media']
        clear_existing = options['clear_existing']
        dry_run = options['dry_run']
        
        if not os.path.exists(backup_file):
            self.stdout.write(
                self.style.ERROR(f'Backup file not found: {backup_file}')
            )
            return
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        if clear_existing and not dry_run:
            if not self._confirm_clear_data():
                self.stdout.write('Restore cancelled')
                return
        
        try:
            with zipfile.ZipFile(backup_file, 'r') as backup_zip:
                # Read and display manifest
                self._read_manifest(backup_zip)
                
                if not dry_run:
                    # Clear existing data if requested
                    if clear_existing:
                        self._clear_existing_data()
                    
                    # Restore database data
                    self._restore_database_data(backup_zip)
                    
                    # Restore media files if requested
                    if restore_media:
                        self._restore_media_files(backup_zip)
                
                self.stdout.write(
                    self.style.SUCCESS('Restore completed successfully!')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during restore: {str(e)}')
            )
    
    def _confirm_clear_data(self):
        """Confirm clearing existing data"""
        self.stdout.write(
            self.style.WARNING(
                'WARNING: This will delete ALL existing data in the database!'
            )
        )
        response = input('Are you sure you want to continue? (yes/no): ')
        return response.lower() == 'yes'
    
    def _read_manifest(self, backup_zip):
        """Read and display backup manifest"""
        try:
            manifest_data = backup_zip.read('manifest.json')
            manifest = json.loads(manifest_data)
            
            self.stdout.write(f"Backup Date: {manifest['backup_date']}")
            self.stdout.write(f"Format: {manifest['format']}")
            self.stdout.write(f"Include Media: {manifest['include_media']}")
            self.stdout.write("Models in backup:")
            
            for model_info in manifest['models']:
                self.stdout.write(f"  - {model_info['verbose_name']}: {model_info['count']} records")
                
        except KeyError:
            self.stdout.write('No manifest found in backup file')
    
    def _clear_existing_data(self):
        """Clear existing data from database"""
        from suptrack.models import (
            QRScanLog, InventoryTransaction, RequestItem, SupplyRequest,
            SupplyItem, SupplyCategory, SavedSearch, SystemConfiguration, UserProfile
        )
        
        # Order matters due to foreign key constraints
        models_to_clear = [
            QRScanLog, InventoryTransaction, RequestItem, SupplyRequest,
            SupplyItem, SupplyCategory, SavedSearch, SystemConfiguration, UserProfile
        ]
        
        self.stdout.write('Clearing existing data...')
        
        with transaction.atomic():
            for model in models_to_clear:
                count = model.objects.count()
                model.objects.all().delete()
                self.stdout.write(f'  - Cleared {count} {model._meta.verbose_name_plural}')
    
    def _restore_database_data(self, backup_zip):
        """Restore database data from backup"""
        from suptrack.models import (
            UserProfile, SupplyCategory, SupplyItem, SupplyRequest,
            RequestItem, QRScanLog, InventoryTransaction, SavedSearch, SystemConfiguration
        )
        
        # Order matters due to foreign key constraints
        models_to_restore = [
            UserProfile, SupplyCategory, SupplyItem, SupplyRequest,
            RequestItem, QRScanLog, InventoryTransaction, SavedSearch, SystemConfiguration
        ]
        
        self.stdout.write('Restoring database data...')
        
        for model in models_to_restore:
            model_name = model._meta.label_lower.replace('.', '_')
            
            # Try both json and xml formats
            for format_ext in ['json', 'xml']:
                filename = f'data/{model_name}.{format_ext}'
                
                try:
                    data = backup_zip.read(filename)
                    break
                except KeyError:
                    continue
            else:
                self.stdout.write(f'  - No data found for {model._meta.verbose_name_plural}')
                continue
            
            # Deserialize and save data
            with transaction.atomic():
                objects = serializers.deserialize(format_ext, data)
                count = 0
                for obj in objects:
                    obj.save()
                    count += 1
                
                self.stdout.write(f'  - Restored {count} {model._meta.verbose_name_plural}')
    
    def _restore_media_files(self, backup_zip):
        """Restore media files from backup"""
        media_root = settings.MEDIA_ROOT
        
        self.stdout.write('Restoring media files...')
        
        # Create media directory if it doesn't exist
        if not os.path.exists(media_root):
            os.makedirs(media_root)
        
        # Extract media files
        for file_info in backup_zip.infolist():
            if file_info.filename.startswith('media/'):
                # Remove 'media/' prefix to get relative path
                relative_path = file_info.filename[6:]
                if relative_path:  # Skip the media/ directory itself
                    target_path = os.path.join(media_root, relative_path)
                    
                    # Create directory if needed
                    target_dir = os.path.dirname(target_path)
                    if not os.path.exists(target_dir):
                        os.makedirs(target_dir)
                    
                    # Extract file
                    with backup_zip.open(file_info) as source, open(target_path, 'wb') as target:
                        target.write(source.read())
        
        self.stdout.write('  - Media files restored')
