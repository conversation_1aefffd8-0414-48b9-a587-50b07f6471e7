from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from django.http import JsonResponse
from .models import UserProfile


class RoleBasedAccessControlMiddleware:
    """
    Middleware for role-based access control validation.
    
    This middleware ensures that users have appropriate roles for accessing
    specific URL patterns and provides consistent access control across the application.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Define URL patterns and their required roles
        self.role_patterns = {
            # Admin-only URLs
            '/admin/': ['admin'],
            '/reports/': ['admin'],
            '/admin-dashboard/': ['admin'],
            '/users/': ['admin'],
            '/system-settings/': ['admin'],

            # GSO Staff URLs - Legacy
            '/gso-dashboard/': ['gso_staff'],
            '/qr-scanner/': ['gso_staff'],
            '/approve-requests/': ['admin', 'gso_staff'],
            '/inventory/': ['admin', 'gso_staff'],
            '/release-supplies/': ['admin', 'gso_staff'],

            # GSO Staff URLs - New namespaced routes
            '/gso/': ['gso_staff'],
            '/gso/dashboard/': ['gso_staff'],
            '/gso/inventory/': ['gso_staff'],
            '/gso/approvals/': ['gso_staff'],
            '/gso/requests/': ['gso_staff'],
            '/gso/qr-scanner/': ['gso_staff'],
            '/gso/reports/': ['gso_staff'],
            '/gso/htmx/': ['gso_staff'],

            # Department User URLs (accessible to all authenticated users)
            '/dashboard/': ['admin', 'gso_staff', 'department_user'],
            '/requests/': ['admin', 'gso_staff', 'department_user'],
            '/create-request/': ['department_user'],
            '/my-requests/': ['department_user'],
            
            # Public URLs (no authentication required)
            '/login/': [],
            '/register/': [],
            '/logout/': [],
            '/': [],
        }
    
    def __call__(self, request):
        # Skip middleware for certain conditions
        if self.should_skip_middleware(request):
            return self.get_response(request)
        
        # Check role-based access for authenticated users
        if request.user.is_authenticated:
            access_granted = self.check_role_based_access(request)
            if not access_granted:
                return self.handle_access_denied(request)
        
        response = self.get_response(request)
        return response
    
    def should_skip_middleware(self, request):
        """
        Determine if middleware should be skipped for this request.
        
        Args:
            request: Django request object
        
        Returns:
            bool: True if middleware should be skipped
        """
        # Skip for static files and media files
        if request.path.startswith('/static/') or request.path.startswith('/media/'):
            return True
        
        # Skip for Django admin (has its own permission system)
        if request.path.startswith('/admin/') and not request.path == '/admin-dashboard/':
            return True
        
        # Skip for API endpoints that handle their own authentication
        if request.path.startswith('/api/'):
            return True
        
        return False
    
    def check_role_based_access(self, request):
        """
        Check if user has appropriate role for the requested URL.
        
        Args:
            request: Django request object
        
        Returns:
            bool: True if access is granted, False otherwise
        """
        path = request.path
        
        # Get user role
        try:
            user_role = request.user.userprofile.role
        except (AttributeError, UserProfile.DoesNotExist):
            # Create default profile for users without one
            UserProfile.objects.create(
                user=request.user,
                role='department_user'
            )
            user_role = 'department_user'
        
        # Check against role patterns
        for pattern, required_roles in self.role_patterns.items():
            if path.startswith(pattern):
                # If no roles required, access is public
                if not required_roles:
                    return True
                
                # Check if user has required role
                if user_role in required_roles:
                    return True
                else:
                    return False
        
        # Default: allow access if no specific pattern matches
        # This allows for flexible URL patterns not explicitly defined
        return True
    
    def handle_access_denied(self, request):
        """
        Handle access denied scenarios.
        
        Args:
            request: Django request object
        
        Returns:
            HttpResponse: Appropriate response for access denied
        """
        # Handle HTMX requests with JSON response
        if request.headers.get('HX-Request'):
            return JsonResponse({
                'success': False,
                'message': 'Access denied. You do not have permission to access this resource.',
                'error_code': 'ACCESS_DENIED',
                'redirect_url': reverse('dashboard')
            }, status=403)
        
        # Handle regular requests with redirect and message
        messages.error(
            request, 
            'Access denied. You do not have permission to access that page.'
        )
        
        # Redirect based on user role
        try:
            user_role = request.user.userprofile.role
            if user_role == 'admin':
                return redirect('admin_dashboard')
            elif user_role == 'gso_staff':
                return redirect('gso_dashboard')
            else:
                return redirect('dashboard')
        except (AttributeError, UserProfile.DoesNotExist):
            return redirect('dashboard')


class UserProfileMiddleware:
    """
    Middleware to ensure all authenticated users have a UserProfile.
    
    This middleware automatically creates UserProfile instances for users
    who don't have one, ensuring consistent role-based functionality.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Ensure authenticated users have a profile
        if request.user.is_authenticated:
            self.ensure_user_profile(request.user)
        
        response = self.get_response(request)
        return response
    
    def ensure_user_profile(self, user):
        """
        Ensure user has a UserProfile instance.
        
        Args:
            user: Django User instance
        """
        try:
            # Try to access the profile
            _ = user.userprofile
        except UserProfile.DoesNotExist:
            # Create default profile if it doesn't exist
            UserProfile.objects.create(
                user=user,
                role='department_user',
                department='',
                phone_number=''
            )


class SecurityHeadersMiddleware:
    """
    Middleware to add security headers for the application.
    
    This middleware adds security-related headers to protect against
    common web vulnerabilities.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        response = self.get_response(request)
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add CSP header for HTMX and other scripts
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' "
            "https://cdn.tailwindcss.com "
            "https://unpkg.com "
            "https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' "
            "https://unpkg.com; "
            "img-src 'self' data: blob:; "
            "connect-src 'self'; "
            "font-src 'self' data:; "
            "media-src 'self';"
        )
        response['Content-Security-Policy'] = csp_policy
        
        return response