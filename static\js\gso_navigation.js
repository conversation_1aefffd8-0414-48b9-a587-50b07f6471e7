/**
 * GSO Navigation JavaScript Module
 * Handles sidebar navigation, submenu toggles, active states, and user interactions
 * for GSO (Government Services Office) users.
 */

class GSONavigation {
    constructor() {
        this.navLinks = null;
        this.navSections = null;
        this.currentPath = window.location.pathname;
        this.storagePrefix = 'gso-nav-';
        
        this.init();
    }
    
    /**
     * Initialize GSO navigation functionality
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    /**
     * Setup navigation functionality
     */
    setup() {
        this.cacheElements();
        this.setActiveNavItem();
        this.initSubmenuToggles();
        this.initNavStatePersistence();
        this.initKeyboardShortcuts();
        this.initMobileNavigation();
        
        // Debug logging
        console.log('GSO Navigation initialized for path:', this.currentPath);
    }
    
    /**
     * Cache DOM elements for better performance
     */
    cacheElements() {
        this.navLinks = document.querySelectorAll('.nav-link, .nav-sublink');
        this.navSections = document.querySelectorAll('.nav-section');
    }
    
    /**
     * Set active navigation item based on current URL
     */
    setActiveNavItem() {
        if (!this.navLinks) return;
        
        // Remove all active states first
        this.navLinks.forEach(link => {
            this.removeActiveClasses(link);
        });
        
        let activeLink = null;
        let activeSection = null;
        
        // Find the best matching link (longest matching path)
        this.navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && this.isPathMatch(href)) {
                if (!activeLink || href.length > activeLink.getAttribute('href').length) {
                    activeLink = link;
                    activeSection = link.closest('.nav-section');
                }
            }
        });
        
        // Apply active state to the best match
        if (activeLink) {
            this.setActiveClasses(activeLink, activeSection);
        }
    }
    
    /**
     * Check if current path matches the given href
     */
    isPathMatch(href) {
        // Exact match first
        if (this.currentPath === href) return true;
        
        // For GSO paths, check if current path starts with href (but not just '/gso/')
        if (href !== '/gso/' && this.currentPath.startsWith(href)) return true;
        
        return false;
    }
    
    /**
     * Remove active classes from a navigation link
     */
    removeActiveClasses(link) {
        link.classList.remove('active', 'bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
        link.classList.add('text-gray-700');
    }
    
    /**
     * Set active classes on a navigation link
     */
    setActiveClasses(activeLink, activeSection) {
        activeLink.classList.remove('text-gray-700');
        activeLink.classList.add('active', 'bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
        
        // Handle submenu parent highlighting
        if (activeLink.classList.contains('nav-sublink') && activeSection) {
            const parentLink = activeSection.querySelector('.nav-link');
            if (parentLink) {
                parentLink.classList.remove('text-gray-700');
                parentLink.classList.add('bg-blue-50', 'text-blue-600');
                
                // Expand the submenu
                this.expandSubmenu(activeSection);
            }
        }
    }
    
    /**
     * Expand a submenu section
     */
    expandSubmenu(section) {
        const submenu = section.querySelector('.nav-submenu');
        const arrow = section.querySelector('.nav-link svg:last-child');
        
        if (submenu) {
            submenu.classList.remove('hidden');
            if (arrow) {
                arrow.classList.add('rotate-180');
            }
        }
    }
    
    /**
     * Initialize submenu toggle functionality
     */
    initSubmenuToggles() {
        if (!this.navSections) return;
        
        this.navSections.forEach(section => {
            const mainLink = section.querySelector('.nav-link');
            const submenu = section.querySelector('.nav-submenu');
            const arrow = mainLink?.querySelector('svg:last-child');
            
            if (mainLink && submenu) {
                mainLink.addEventListener('click', (e) => {
                    this.handleSubmenuToggle(e, section, submenu, arrow);
                });
            }
        });
    }
    
    /**
     * Handle submenu toggle click
     */
    handleSubmenuToggle(event, section, submenu, arrow) {
        const isArrowClick = event.target.closest('svg:last-child');
        const isSubmenuHidden = submenu.classList.contains('hidden');
        const hasSubmenuItems = submenu.querySelectorAll('.nav-sublink').length > 0;
        
        // Only prevent default if clicking arrow or if submenu is hidden and has items
        if (isArrowClick || (isSubmenuHidden && hasSubmenuItems)) {
            event.preventDefault();
            
            // Toggle submenu visibility
            submenu.classList.toggle('hidden');
            
            // Toggle arrow rotation
            if (arrow) {
                arrow.classList.toggle('rotate-180');
            }
            
            // Save state to localStorage
            this.saveSubmenuState(section, !submenu.classList.contains('hidden'));
        }
    }
    
    /**
     * Save submenu state to localStorage
     */
    saveSubmenuState(section, isOpen) {
        const sectionName = section.getAttribute('data-nav-section');
        if (sectionName) {
            localStorage.setItem(`${this.storagePrefix}${sectionName}`, isOpen.toString());
        }
    }
    
    /**
     * Initialize navigation state persistence
     */
    initNavStatePersistence() {
        if (!this.navSections) return;
        
        this.navSections.forEach(section => {
            const sectionName = section.getAttribute('data-nav-section');
            if (sectionName) {
                const isOpen = localStorage.getItem(`${this.storagePrefix}${sectionName}`) === 'true';
                if (isOpen) {
                    this.expandSubmenu(section);
                }
            }
        });
    }
    
    /**
     * Initialize keyboard shortcuts for GSO users
     */
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle shortcuts if Alt key is pressed and no input is focused
            if (!e.altKey || this.isInputFocused()) return;
            
            switch (e.key.toLowerCase()) {
                case 'd':
                    e.preventDefault();
                    this.navigateToUrl('gso_dashboard_main');
                    break;
                case 'i':
                    e.preventDefault();
                    this.navigateToUrl('gso_inventory');
                    break;
                case 'a':
                    e.preventDefault();
                    this.navigateToUrl('gso_approvals');
                    break;
                case 'r':
                    e.preventDefault();
                    this.navigateToUrl('gso_requests');
                    break;
                case 'q':
                    e.preventDefault();
                    this.navigateToUrl('gso_qr_scanner');
                    break;
            }
        });
    }
    
    /**
     * Check if an input element is currently focused
     */
    isInputFocused() {
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.tagName === 'SELECT' ||
            activeElement.isContentEditable
        );
    }
    
    /**
     * Navigate to a URL by name (requires Django URL resolution)
     */
    navigateToUrl(urlName) {
        // This would need to be implemented with Django URL resolution
        // For now, we'll use a simple mapping
        const urlMap = {
            'gso_dashboard_main': '/gso/dashboard/',
            'gso_inventory': '/gso/inventory/',
            'gso_approvals': '/gso/approvals/',
            'gso_requests': '/gso/requests/',
            'gso_qr_scanner': '/gso/qr-scanner/'
        };
        
        const url = urlMap[urlName];
        if (url) {
            window.location.href = url;
        }
    }
    
    /**
     * Initialize mobile navigation enhancements
     */
    initMobileNavigation() {
        // Add touch-friendly interactions for mobile devices
        if ('ontouchstart' in window) {
            this.navLinks.forEach(link => {
                link.addEventListener('touchstart', () => {
                    // Add visual feedback for touch
                    link.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
                });
                
                link.addEventListener('touchend', () => {
                    // Remove visual feedback
                    setTimeout(() => {
                        link.style.backgroundColor = '';
                    }, 150);
                });
            });
        }
    }
    
    /**
     * Refresh navigation state (useful for dynamic content)
     */
    refresh() {
        this.currentPath = window.location.pathname;
        this.cacheElements();
        this.setActiveNavItem();
    }
    
    /**
     * Get current navigation state
     */
    getState() {
        const state = {
            currentPath: this.currentPath,
            activeLinks: [],
            openSubmenus: []
        };
        
        // Get active links
        document.querySelectorAll('.nav-link.active, .nav-sublink.active').forEach(link => {
            state.activeLinks.push({
                href: link.getAttribute('href'),
                text: link.textContent.trim(),
                isSublink: link.classList.contains('nav-sublink')
            });
        });
        
        // Get open submenus
        document.querySelectorAll('.nav-submenu:not(.hidden)').forEach(submenu => {
            const section = submenu.closest('.nav-section');
            const sectionName = section?.getAttribute('data-nav-section');
            if (sectionName) {
                state.openSubmenus.push(sectionName);
            }
        });
        
        return state;
    }
}

// Global functions for backward compatibility
let gsoNavigation = null;

function initGSONavigation() {
    if (!gsoNavigation) {
        gsoNavigation = new GSONavigation();
    }
    return gsoNavigation;
}

function setActiveNavItem() {
    if (gsoNavigation) {
        gsoNavigation.setActiveNavItem();
    }
}

function initSubmenuToggles() {
    if (gsoNavigation) {
        gsoNavigation.initSubmenuToggles();
    }
}

function initNavStatePersistence() {
    if (gsoNavigation) {
        gsoNavigation.initNavStatePersistence();
    }
}

// Auto-initialize when script loads
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.nav-link[data-nav-item]')) {
        initGSONavigation();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GSONavigation, initGSONavigation };
}
