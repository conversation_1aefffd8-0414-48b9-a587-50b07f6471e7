"""
Tests for QR code views
"""
from django.test import TestCase, Client, override_settings
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from suptrack.models import (
    UserProfile, SupplyCategory, SupplyItem,
    QRScanLog, SupplyRequest, RequestItem
)
import json
import uuid


class QRScannerViewTest(TestCase):
    """Test QR scanner view"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test users
        self.gso_user = User.objects.create_user(
            username='gso_staff',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.create(
            user=self.gso_user,
            role='gso_staff',
            department='GSO'
        )
        
        self.dept_user = User.objects.create_user(
            username='dept_user',
            password='testpass123'
        )
        self.dept_profile = UserProfile.objects.create(
            user=self.dept_user,
            role='department_user',
            department='IT'
        )
        
        # Create test data
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category'
        )
        
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
    
    def test_qr_scanner_access_gso_staff(self):
        """Test QR scanner access for GSO staff"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('qr_scanner'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'QR Code Scanner')
    
    @override_settings(
        MIDDLEWARE=[
            'django.middleware.security.SecurityMiddleware',
            'django.contrib.sessions.middleware.SessionMiddleware',
            'django.middleware.common.CommonMiddleware',
            'django.middleware.csrf.CsrfViewMiddleware',
            'django.contrib.auth.middleware.AuthenticationMiddleware',
            'django.contrib.messages.middleware.MessageMiddleware',
            'django.middleware.clickjacking.XFrameOptionsMiddleware',
            'suptrack.middleware.RoleBasedAccessControlMiddleware',
        ]
    )
    def test_qr_scanner_access_denied_dept_user(self):
        """Test QR scanner access denied for department users"""
        self.client.login(username='dept_user', password='testpass123')

        response = self.client.get(reverse('qr_scanner'))

        # Should redirect or return 403
        self.assertIn(response.status_code, [302, 403])
    
    def test_qr_scanner_requires_login(self):
        """Test QR scanner requires authentication"""
        response = self.client.get(reverse('qr_scanner'))
        
        # Should redirect to login
        self.assertEqual(response.status_code, 302)


class ProcessQRScanViewTest(TestCase):
    """Test QR scan processing view"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        self.gso_user = User.objects.create_user(
            username='gso_staff',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.create(
            user=self.gso_user,
            role='gso_staff',
            department='GSO'
        )
        
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category'
        )
        
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
        
        self.client.login(username='gso_staff', password='testpass123')
    
    def test_process_qr_scan_inventory_check(self):
        """Test processing QR scan for inventory check"""
        scan_data = {
            'qr_code_data': self.supply_item.qr_code_data,
            'scan_type': 'inventory_check',
            'location': 'Warehouse A',
            'notes': 'Regular check'
        }
        
        response = self.client.post(
            reverse('process_qr_scan'),
            data=json.dumps(scan_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        response_data = json.loads(response.content)
        self.assertTrue(response_data['success'])
        
        # Check that scan log was created
        scan_log = QRScanLog.objects.filter(
            supply_item=self.supply_item,
            scanned_by=self.gso_user,
            scan_type='inventory_check'
        ).first()
        
        self.assertIsNotNone(scan_log)
        self.assertEqual(scan_log.location, 'Warehouse A')
        self.assertEqual(scan_log.notes, 'Regular check')
    
    def test_process_qr_scan_invalid_qr_code(self):
        """Test processing QR scan with invalid QR code"""
        scan_data = {
            'qr_code_data': 'invalid-qr-code-data',
            'scan_type': 'inventory_check'
        }
        
        response = self.client.post(
            reverse('process_qr_scan'),
            data=json.dumps(scan_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 404)
        
        response_data = json.loads(response.content)
        self.assertFalse(response_data['success'])
        self.assertIn('not found', response_data['message'].lower())
    
    def test_process_qr_scan_missing_data(self):
        """Test processing QR scan with missing data"""
        scan_data = {
            'scan_type': 'inventory_check'
            # Missing qr_code_data
        }
        
        response = self.client.post(
            reverse('process_qr_scan'),
            data=json.dumps(scan_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        
        response_data = json.loads(response.content)
        self.assertFalse(response_data['success'])
    
    def test_process_qr_scan_issuance_with_request_item(self):
        """Test processing QR scan for issuance with request item"""
        # Create a supply request and request item
        request = SupplyRequest.objects.create(
            request_number='REQ-001',
            requester=self.gso_user,
            department='IT',
            status='approved'
        )
        
        request_item = RequestItem.objects.create(
            request=request,
            supply_item=self.supply_item,
            quantity_requested=5,
            quantity_approved=5
        )
        
        scan_data = {
            'qr_code_data': self.supply_item.qr_code_data,
            'scan_type': 'issuance',
            'request_item_id': request_item.id,
            'location': 'Warehouse A'
        }
        
        response = self.client.post(
            reverse('process_qr_scan'),
            data=json.dumps(scan_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        response_data = json.loads(response.content)
        self.assertTrue(response_data['success'])
        
        # Check that scan log was created with request item
        scan_log = QRScanLog.objects.filter(
            supply_item=self.supply_item,
            scan_type='issuance',
            request_item=request_item
        ).first()
        
        self.assertIsNotNone(scan_log)
        self.assertEqual(scan_log.request_item, request_item)
    
    def test_process_qr_scan_return_with_quantity(self):
        """Test processing QR scan for return with quantity"""
        scan_data = {
            'qr_code_data': self.supply_item.qr_code_data,
            'scan_type': 'return',
            'quantity': 3,
            'location': 'Warehouse A',
            'notes': 'Returned items'
        }
        
        response = self.client.post(
            reverse('process_qr_scan'),
            data=json.dumps(scan_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        
        response_data = json.loads(response.content)
        self.assertTrue(response_data['success'])
        
        # Check that scan log was created
        scan_log = QRScanLog.objects.filter(
            supply_item=self.supply_item,
            scan_type='return'
        ).first()
        
        self.assertIsNotNone(scan_log)
        self.assertEqual(scan_log.notes, 'Returned items')


class ScanHistoryViewTest(TestCase):
    """Test scan history view"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        self.gso_user = User.objects.create_user(
            username='gso_staff',
            password='testpass123'
        )
        self.gso_profile = UserProfile.objects.create(
            user=self.gso_user,
            role='gso_staff',
            department='GSO'
        )
        
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category'
        )
        
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
        
        # Create some scan logs
        QRScanLog.objects.create(
            supply_item=self.supply_item,
            scanned_by=self.gso_user,
            scan_type='inventory_check',
            location='Warehouse A'
        )
        
        QRScanLog.objects.create(
            supply_item=self.supply_item,
            scanned_by=self.gso_user,
            scan_type='issuance',
            location='Warehouse B'
        )
        
        self.client.login(username='gso_staff', password='testpass123')
    
    def test_scan_history_view(self):
        """Test scan history view displays correctly"""
        response = self.client.get(reverse('scan_history'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'QR Scan History')
        self.assertContains(response, 'Test Item')
    
    def test_scan_history_filtering_by_scan_type(self):
        """Test filtering scan history by scan type"""
        response = self.client.get(reverse('scan_history'), {'scan_type': 'inventory_check'})
        
        self.assertEqual(response.status_code, 200)
        # Should contain inventory check scan but not issuance
        self.assertContains(response, 'Inventory Check')
    
    def test_scan_history_search(self):
        """Test searching scan history"""
        response = self.client.get(reverse('scan_history'), {'search': 'Test Item'})
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Item')
    
    def test_scan_history_htmx_request(self):
        """Test HTMX request returns partial template"""
        response = self.client.get(
            reverse('scan_history'),
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200)
        # Should return partial template, not full page
        self.assertNotContains(response, '<html>')


class QRCodeManagementViewTest(TestCase):
    """Test QR code management views"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        self.admin_user = User.objects.create_user(
            username='admin',
            password='testpass123'
        )
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            role='admin',
            department='Admin'
        )
        
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category'
        )
        
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
        
        self.client.login(username='admin', password='testpass123')
    
    def test_qr_code_management_view(self):
        """Test QR code management dashboard"""
        response = self.client.get(reverse('qr_code_management'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'QR Code Management')
    
    def test_generate_qr_code_view(self):
        """Test QR code generation view"""
        response = self.client.post(reverse('generate_qr_code', args=[self.supply_item.id]))
        
        # Should redirect or return success
        self.assertIn(response.status_code, [200, 302])
        
        # Check that QR code was generated
        self.supply_item.refresh_from_db()
        # QR code file might be generated (depending on implementation)
