from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required

def home(request):
    """Home view that shows landing page for unauthenticated users or redirects to appropriate dashboard"""
    if request.user.is_authenticated:
        # Check user role and redirect to appropriate dashboard
        try:
            profile = request.user.userprofile
            if profile.role == 'gso_staff':
                return redirect('gso_dashboard_main')
            elif profile.role == 'admin':
                return redirect('admin_dashboard')
            else:
                return redirect('dashboard')
        except:
            # If no profile exists, redirect to default dashboard
            return redirect('dashboard')
    else:
        return render(request, 'landing.html')
