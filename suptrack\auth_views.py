from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.urls import reverse
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.cache import never_cache
from django.http import JsonResponse
from django.template.loader import render_to_string
from django.db import models
from .models import UserProfile
from .forms import CustomUserCreationForm, CustomAuthenticationForm


@csrf_protect
@never_cache
def custom_login(request):
    """Custom login view with role-based redirection"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    
    if request.method == 'POST':
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            
            if user is not None:
                login(request, user)
                
                # Get or create user profile
                try:
                    profile = user.userprofile
                except UserProfile.DoesNotExist:
                    # Create default profile for existing users
                    profile = UserProfile.objects.create(
                        user=user,
                        role='department_user'
                    )
                
                # Role-based redirection
                next_url = request.GET.get('next')
                if next_url:
                    return redirect(next_url)
                
                # Default role-based dashboard redirection
                if profile.role == 'admin':
                    messages.success(request, f'Welcome back, {user.get_full_name() or user.username}! You have admin access.')
                    return redirect('admin_dashboard')
                elif profile.role == 'gso_staff':
                    messages.success(request, f'Welcome back, {user.get_full_name() or user.username}! GSO Staff dashboard loaded.')
                    return redirect('gso_dashboard')
                else:
                    messages.success(request, f'Welcome back, {user.get_full_name() or user.username}!')
                    return redirect('dashboard')
            else:
                messages.error(request, 'Invalid username or password.')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = CustomAuthenticationForm()
    
    return render(request, 'registration/login.html', {'form': form})


@csrf_protect
def custom_register(request):
    """Custom registration view with role assignment"""
    if request.user.is_authenticated:
        return redirect('dashboard')
    
    if request.method == 'POST':
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            user = form.save()
            
            # Create user profile with selected role
            UserProfile.objects.create(
                user=user,
                role=form.cleaned_data.get('role', 'department_user'),
                department=form.cleaned_data.get('department', ''),
                phone_number=form.cleaned_data.get('phone_number', '')
            )
            
            # Auto-login after registration
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password1')
            user = authenticate(username=username, password=password)
            
            if user is not None:
                login(request, user)
                messages.success(request, f'Account created successfully! Welcome, {user.get_full_name() or user.username}!')
                
                # Role-based redirection after registration
                profile = user.userprofile
                if profile.role == 'admin':
                    return redirect('admin_dashboard')
                elif profile.role == 'gso_staff':
                    return redirect('gso_dashboard')
                else:
                    return redirect('dashboard')
            
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = CustomUserCreationForm()
    
    return render(request, 'registration/register.html', {'form': form})


@login_required
def custom_logout(request):
    """Custom logout view"""
    user_name = request.user.get_full_name() or request.user.username
    logout(request)
    messages.success(request, f'You have been logged out successfully. Goodbye, {user_name}!')
    return redirect('login')


@login_required
def dashboard(request):
    """Main dashboard view with role-specific content"""
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        # Create default profile for existing users
        profile = UserProfile.objects.create(
            user=request.user,
            role='department_user'
        )
    
    # Get dashboard statistics based on role
    context = {
        'user_profile': profile,
        'stats': get_dashboard_stats(request.user, profile.role),
        'recent_activities': get_recent_activities(request.user, profile.role)
    }
    
    return render(request, 'dashboard.html', context)


@login_required
def admin_dashboard(request):
    """Admin-specific dashboard"""
    if not hasattr(request.user, 'userprofile') or request.user.userprofile.role != 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('dashboard')
    
    context = {
        'user_profile': request.user.userprofile,
        'stats': get_admin_dashboard_stats(),
        'recent_activities': get_admin_recent_activities()
    }
    
    return render(request, 'admin_dashboard.html', context)


@login_required
def gso_dashboard(request):
    """GSO Staff-specific dashboard"""
    if not hasattr(request.user, 'userprofile') or request.user.userprofile.role != 'gso_staff':
        messages.error(request, 'Access denied. GSO Staff privileges required.')
        return redirect('dashboard')
    
    context = {
        'user_profile': request.user.userprofile,
        'stats': get_gso_dashboard_stats(),
        'recent_activities': get_gso_recent_activities()
    }
    
    return render(request, 'gso_dashboard.html', context)


def get_dashboard_stats(user, role):
    """Get dashboard statistics based on user role"""
    from django.utils import timezone
    from django.db.models import Count, Q, F
    from .models import SupplyRequest, SupplyItem
    
    today = timezone.now().date()
    stats = {}
    
    if role == 'admin':
        # Admin sees all statistics
        stats = {
            'total_requests_today': SupplyRequest.objects.filter(request_date__date=today).count(),
            'pending_requests': SupplyRequest.objects.filter(status='pending').count(),
            'approved_today': SupplyRequest.objects.filter(
                approved_date__date=today,
                status='approved'
            ).count(),
            'low_stock_items': SupplyItem.objects.filter(
                current_stock__lte=F('minimum_stock')
            ).count()
        }
    elif role == 'gso_staff':
        # GSO Staff sees approval-related statistics
        stats = {
            'total_requests_today': SupplyRequest.objects.filter(request_date__date=today).count(),
            'pending_requests': SupplyRequest.objects.filter(status='pending').count(),
            'approved_today': SupplyRequest.objects.filter(
                approved_date__date=today,
                status='approved'
            ).count(),
            'low_stock_items': SupplyItem.objects.filter(
                current_stock__lte=F('minimum_stock')
            ).count()
        }
    else:
        # Department users see their own statistics
        stats = {
            'total_requests_today': SupplyRequest.objects.filter(
                requester=user,
                request_date__date=today
            ).count(),
            'pending_requests': SupplyRequest.objects.filter(
                requester=user,
                status='pending'
            ).count(),
            'approved_today': SupplyRequest.objects.filter(
                requester=user,
                approved_date__date=today,
                status='approved'
            ).count(),
            'total_requests': SupplyRequest.objects.filter(requester=user).count(),
            'low_stock_items': 0  # Department users don't see stock info
        }
    
    return stats


def get_recent_activities(user, role):
    """Get recent activities based on user role"""
    from django.utils import timezone
    from .models import SupplyRequest, QRScanLog, InventoryTransaction
    
    activities = []
    
    if role == 'admin':
        # Admin sees all recent activities
        recent_requests = SupplyRequest.objects.select_related('requester').order_by('-request_date')[:5]
        for request in recent_requests:
            activities.append({
                'description': f'New supply request #{request.request_number} from {request.requester.get_full_name() or request.requester.username}',
                'timestamp': request.request_date
            })
    elif role == 'gso_staff':
        # GSO Staff sees approval-related activities
        recent_requests = SupplyRequest.objects.filter(status='pending').select_related('requester').order_by('-request_date')[:5]
        for request in recent_requests:
            activities.append({
                'description': f'Pending approval: Request #{request.request_number} from {request.requester.get_full_name() or request.requester.username}',
                'timestamp': request.request_date
            })
    else:
        # Department users see their own activities
        recent_requests = SupplyRequest.objects.filter(requester=user).order_by('-request_date')[:5]
        for request in recent_requests:
            activities.append({
                'description': f'Your request #{request.request_number} is {request.get_status_display().lower()}',
                'timestamp': request.request_date
            })
    
    return activities


def get_admin_dashboard_stats():
    """Get admin-specific dashboard statistics"""
    # Implementation for admin-specific stats
    return {}


def get_admin_recent_activities():
    """Get admin-specific recent activities"""
    # Implementation for admin-specific activities
    return []


def get_gso_dashboard_stats():
    """Get GSO staff-specific dashboard statistics"""
    # Implementation for GSO-specific stats
    return {}


def get_gso_recent_activities():
    """Get GSO staff-specific recent activities"""
    # Implementation for GSO-specific activities
    return []


@login_required
def dashboard_stats_htmx(request):
    """HTMX endpoint for real-time dashboard statistics updates"""
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(
            user=request.user,
            role='department_user'
        )
    
    stats = get_dashboard_stats(request.user, profile.role)
    
    # Return partial template for HTMX
    return render(request, 'partials/dashboard_stats.html', {
        'stats': stats,
        'user_profile': profile
    })


@login_required
def dashboard_activities_htmx(request):
    """HTMX endpoint for real-time dashboard activities updates"""
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(
            user=request.user,
            role='department_user'
        )
    
    activities = get_recent_activities(request.user, profile.role)
    
    # Return partial template for HTMX
    return render(request, 'partials/dashboard_activities.html', {
        'recent_activities': activities,
        'user_profile': profile
    })


@login_required
def dashboard_widgets_htmx(request):
    """HTMX endpoint for role-specific dashboard widgets"""
    try:
        profile = request.user.userprofile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(
            user=request.user,
            role='department_user'
        )
    
    # Get role-specific widget data
    widget_data = get_role_specific_widgets(request.user, profile.role)
    
    # Return partial template for HTMX
    return render(request, 'partials/dashboard_widgets.html', {
        'widgets': widget_data,
        'user_profile': profile
    })


def get_role_specific_widgets(user, role):
    """Get role-specific widget data"""
    from django.utils import timezone
    from django.db.models import Count, Q, F
    from .models import SupplyRequest, SupplyItem, QRScanLog
    
    widgets = []
    
    if role == 'admin':
        # Admin widgets
        low_stock_count = SupplyItem.objects.filter(current_stock__lte=models.F('minimum_stock')).count()
        widgets = [
            {
                'title': 'Inventory Management',
                'description': f'Manage supply inventory and stock levels',
                'icon': 'inventory',
                'color': 'blue',
                'action_text': 'Manage Inventory',
                'action_url': reverse('inventory_list'),
                'badge': low_stock_count if low_stock_count > 0 else None
            },
            {
                'title': 'User Management',
                'description': 'Manage user accounts and roles',
                'icon': 'users',
                'color': 'green',
                'action_text': 'Manage Users',
                'action_url': '#'
            },
            {
                'title': 'System Reports',
                'description': 'View comprehensive system reports',
                'icon': 'chart',
                'color': 'purple',
                'action_text': 'View Reports',
                'action_url': '#'
            },
            {
                'title': 'System Settings',
                'description': 'Configure system settings',
                'icon': 'settings',
                'color': 'gray',
                'action_text': 'Settings',
                'action_url': '#'
            }
        ]
    elif role == 'gso_staff':
        # GSO Staff widgets
        pending_count = SupplyRequest.objects.filter(status='pending').count()
        low_stock_count = SupplyItem.objects.filter(current_stock__lte=models.F('minimum_stock')).count()
        widgets = [
            {
                'title': 'Approve Requests',
                'description': f'Review and approve {pending_count} pending supply requests',
                'icon': 'check',
                'color': 'blue',
                'action_text': 'View Pending',
                'action_url': reverse('pending_requests_view'),
                'badge': pending_count if pending_count > 0 else None
            },
            {
                'title': 'Inventory Management',
                'description': 'Manage supply inventory and stock levels',
                'icon': 'inventory',
                'color': 'purple',
                'action_text': 'Manage Inventory',
                'action_url': reverse('inventory_list'),
                'badge': low_stock_count if low_stock_count > 0 else None
            },
            {
                'title': 'QR Scanner',
                'description': 'Scan QR codes for supply tracking',
                'icon': 'qr',
                'color': 'green',
                'action_text': 'Open Scanner',
                'action_url': reverse('qr_scanner')
            }
        ]
    else:
        # Department User widgets
        user_pending = SupplyRequest.objects.filter(requester=user, status='pending').count()
        widgets = [
            {
                'title': 'Create Request',
                'description': 'Submit a new supply request',
                'icon': 'plus',
                'color': 'blue',
                'action_text': 'New Request',
                'action_url': reverse('create_request')
            },
            {
                'title': 'My Requests',
                'description': f'View your {user_pending} pending requests',
                'icon': 'list',
                'color': 'green',
                'action_text': 'View Requests',
                'action_url': reverse('request_list'),
                'badge': user_pending if user_pending > 0 else None
            }
        ]
    
    return widgets