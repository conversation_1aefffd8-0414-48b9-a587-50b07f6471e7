"""
Tests for QR code related models
"""
from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from suptrack.models import (
    UserProfile, SupplyCategory, SupplyItem, 
    QRScanLog, InventoryTransaction, SupplyRequest, RequestItem
)
import uuid


class SupplyItemModelTest(TestCase):
    """Test SupplyItem model QR code functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category for QR testing'
        )
        
    def test_qr_code_data_auto_generation(self):
        """Test that QR code data is automatically generated"""
        item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
        
        # QR code data should be auto-generated
        self.assertIsNotNone(item.qr_code_data)
        self.assertTrue(len(item.qr_code_data) > 0)
        
        # Should be a valid UUID format
        try:
            uuid.UUID(item.qr_code_data)
        except ValueError:
            self.fail("QR code data is not a valid UUID")
    
    def test_qr_code_data_uniqueness(self):
        """Test that QR code data is unique across items"""
        item1 = SupplyItem.objects.create(
            name='Test Item 1',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
        
        item2 = SupplyItem.objects.create(
            name='Test Item 2',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=15,
            minimum_stock=3
        )
        
        self.assertNotEqual(item1.qr_code_data, item2.qr_code_data)
    
    def test_qr_code_data_preservation(self):
        """Test that existing QR code data is preserved on save"""
        original_qr_data = str(uuid.uuid4())
        item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5,
            qr_code_data=original_qr_data
        )
        
        # Update item and save
        item.current_stock = 20
        item.save()
        
        # QR code data should remain the same
        self.assertEqual(item.qr_code_data, original_qr_data)
    
    def test_stock_level_properties(self):
        """Test stock level helper properties"""
        # Test low stock
        low_stock_item = SupplyItem.objects.create(
            name='Low Stock Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=3,
            minimum_stock=5
        )
        self.assertTrue(low_stock_item.is_low_stock)
        self.assertFalse(low_stock_item.is_out_of_stock)
        
        # Test out of stock
        out_of_stock_item = SupplyItem.objects.create(
            name='Out of Stock Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=0,
            minimum_stock=5
        )
        self.assertTrue(out_of_stock_item.is_low_stock)
        self.assertTrue(out_of_stock_item.is_out_of_stock)
        
        # Test normal stock
        normal_stock_item = SupplyItem.objects.create(
            name='Normal Stock Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=20,
            minimum_stock=5
        )
        self.assertFalse(normal_stock_item.is_low_stock)
        self.assertFalse(normal_stock_item.is_out_of_stock)


class QRScanLogModelTest(TestCase):
    """Test QRScanLog model functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.profile = UserProfile.objects.create(
            user=self.user,
            role='gso_staff',
            department='GSO'
        )
        
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category'
        )
        
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
    
    def test_scan_log_creation(self):
        """Test creating a QR scan log"""
        scan_log = QRScanLog.objects.create(
            supply_item=self.supply_item,
            scanned_by=self.user,
            scan_type='inventory_check',
            location='Warehouse A',
            notes='Regular inventory check'
        )
        
        self.assertEqual(scan_log.supply_item, self.supply_item)
        self.assertEqual(scan_log.scanned_by, self.user)
        self.assertEqual(scan_log.scan_type, 'inventory_check')
        self.assertEqual(scan_log.location, 'Warehouse A')
        self.assertEqual(scan_log.notes, 'Regular inventory check')
        self.assertIsNotNone(scan_log.scan_datetime)
    
    def test_scan_type_choices(self):
        """Test that only valid scan types are accepted"""
        valid_types = ['issuance', 'return', 'inventory_check']
        
        for scan_type in valid_types:
            scan_log = QRScanLog.objects.create(
                supply_item=self.supply_item,
                scanned_by=self.user,
                scan_type=scan_type
            )
            self.assertEqual(scan_log.scan_type, scan_type)
    
    def test_scan_log_with_request_item(self):
        """Test scan log with associated request item"""
        # Create a supply request and request item
        request = SupplyRequest.objects.create(
            request_number='REQ-001',
            requester=self.user,
            department='IT',
            status='approved'
        )
        
        request_item = RequestItem.objects.create(
            request=request,
            supply_item=self.supply_item,
            quantity_requested=5,
            quantity_approved=5
        )
        
        scan_log = QRScanLog.objects.create(
            supply_item=self.supply_item,
            scanned_by=self.user,
            scan_type='issuance',
            request_item=request_item
        )
        
        self.assertEqual(scan_log.request_item, request_item)
        self.assertEqual(scan_log.scan_type, 'issuance')


class InventoryTransactionModelTest(TestCase):
    """Test InventoryTransaction model functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category'
        )
        
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
    
    def test_inventory_transaction_creation(self):
        """Test creating an inventory transaction"""
        transaction = InventoryTransaction.objects.create(
            supply_item=self.supply_item,
            transaction_type='out',
            quantity=3,
            previous_stock=10,
            new_stock=7,
            performed_by=self.user,
            reference_number='REF-001',
            notes='Test transaction'
        )
        
        self.assertEqual(transaction.supply_item, self.supply_item)
        self.assertEqual(transaction.transaction_type, 'out')
        self.assertEqual(transaction.quantity, 3)
        self.assertEqual(transaction.previous_stock, 10)
        self.assertEqual(transaction.new_stock, 7)
        self.assertEqual(transaction.performed_by, self.user)
        self.assertEqual(transaction.reference_number, 'REF-001')
        self.assertEqual(transaction.notes, 'Test transaction')
        self.assertIsNotNone(transaction.transaction_date)
    
    def test_transaction_type_choices(self):
        """Test that only valid transaction types are accepted"""
        valid_types = ['in', 'out', 'adjustment']
        
        for transaction_type in valid_types:
            transaction = InventoryTransaction.objects.create(
                supply_item=self.supply_item,
                transaction_type=transaction_type,
                quantity=1,
                previous_stock=10,
                new_stock=11 if transaction_type == 'in' else 9,
                performed_by=self.user
            )
            self.assertEqual(transaction.transaction_type, transaction_type)
