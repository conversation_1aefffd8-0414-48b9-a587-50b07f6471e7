<!-- Request Analytics -->
<div class="mb-8">
    <h2 class="text-xl font-semibold text-gray-900 mb-4">Request Analytics ({{ days }} days)</h2>
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600">{{ request_analytics.total_requests }}</p>
                <p class="text-sm text-gray-600">Total Requests</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-yellow-600">{{ request_analytics.pending_requests }}</p>
                <p class="text-sm text-gray-600">Pending</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600">{{ request_analytics.approved_requests }}</p>
                <p class="text-sm text-gray-600">Approved</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-red-600">{{ request_analytics.rejected_requests }}</p>
                <p class="text-sm text-gray-600">Rejected</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-purple-600">{{ request_analytics.released_requests }}</p>
                <p class="text-sm text-gray-600">Released</p>
            </div>
        </div>
    </div>
</div>

<!-- Inventory Analytics -->
<div class="mb-8">
    <h2 class="text-xl font-semibold text-gray-900 mb-4">Inventory Analytics</h2>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600">{{ inventory_analytics.total_items }}</p>
                <p class="text-sm text-gray-600">Total Items</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-yellow-600">{{ inventory_analytics.low_stock_items }}</p>
                <p class="text-sm text-gray-600">Low Stock</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-red-600">{{ inventory_analytics.out_of_stock_items }}</p>
                <p class="text-sm text-gray-600">Out of Stock</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600">{{ inventory_analytics.total_transactions }}</p>
                <p class="text-sm text-gray-600">Transactions ({{ days }}d)</p>
            </div>
        </div>
    </div>
</div>

<!-- QR Scan Analytics -->
<div class="mb-8">
    <h2 class="text-xl font-semibold text-gray-900 mb-4">QR Scan Analytics ({{ days }} days)</h2>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600">{{ scan_analytics.total_scans }}</p>
                <p class="text-sm text-gray-600">Total Scans</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600">{{ scan_analytics.inventory_scans }}</p>
                <p class="text-sm text-gray-600">Inventory Checks</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-purple-600">{{ scan_analytics.issuance_scans }}</p>
                <p class="text-sm text-gray-600">Issuances</p>
            </div>
        </div>
        <div class="bg-white rounded-lg shadow p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-orange-600">{{ scan_analytics.return_scans }}</p>
                <p class="text-sm text-gray-600">Returns</p>
            </div>
        </div>
    </div>
</div>

<!-- Department Activity -->
<div class="bg-white rounded-lg shadow-md p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Department Activity ({{ days }} days)</h3>
    <div class="space-y-3">
        {% for dept in dept_activity %}
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
            <div>
                <p class="font-medium text-gray-900">{{ dept.department }}</p>
                <p class="text-sm text-gray-600">{{ dept.request_count }} requests</p>
            </div>
            <div class="text-right text-sm">
                {% if dept.avg_approval_time %}
                    <p class="text-gray-900">Avg approval: {{ dept.avg_approval_time|floatformat:1 }} days</p>
                {% else %}
                    <p class="text-gray-500">No approval data</p>
                {% endif %}
            </div>
        </div>
        {% empty %}
        <div class="text-center py-8">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <p class="text-gray-500">No department activity data available for the selected period</p>
        </div>
        {% endfor %}
    </div>
</div>
