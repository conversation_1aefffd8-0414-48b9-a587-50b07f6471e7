{% extends 'base.html' %}

{% block title %}GSO Dashboard - Smart Supply Management{% endblock %}

{% block page_title %}GSO Dashboard{% endblock %}

{% block sidebar_nav %}
    {% include 'partials/gso_sidebar_nav.html' %}
{% endblock %}

{% block content %}
<!-- Dashboard Statistics with HTMX auto-refresh -->
<div id="dashboard-stats" 
     hx-get="{% url 'dashboard_stats_htmx' %}" 
     hx-trigger="load, every 30s"
     hx-swap="innerHTML"
     class="mb-8">
    {% include 'partials/dashboard_stats.html' %}
</div>

<!-- GSO Quick Actions with HTMX -->
<div id="dashboard-widgets" 
     hx-get="{% url 'dashboard_widgets_htmx' %}" 
     hx-trigger="load, every 60s"
     hx-swap="innerHTML"
     class="mb-8">
    {% include 'partials/dashboard_widgets.html' %}
</div>

<!-- GSO Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Pending Approvals Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Pending Approvals</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.pending_requests|default:0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="{% url 'gso_approvals' %}" class="font-medium text-blue-700 hover:text-blue-900">View all</a>
            </div>
        </div>
    </div>

    <!-- Low Stock Items Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Low Stock Items</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.low_stock_items|default:0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="{% url 'gso_low_stock' %}" class="font-medium text-blue-700 hover:text-blue-900">View all</a>
            </div>
        </div>
    </div>

    <!-- Total Inventory Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Items</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.total_items|default:0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="{% url 'gso_inventory' %}" class="font-medium text-blue-700 hover:text-blue-900">View all</a>
            </div>
        </div>
    </div>

    <!-- QR Scans Today Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">QR Scans Today</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.qr_scans_today|default:0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
                <a href="{% url 'gso_qr_scanner' %}" class="font-medium text-blue-700 hover:text-blue-900">View scanner</a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Activities</h3>
            <div class="flex items-center space-x-2">
                <a href="{% url 'gso_requests' %}" 
                   class="text-sm text-blue-600 hover:text-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    View All
                </a>
                <button hx-get="{% url 'dashboard_activities_htmx' %}" 
                        hx-target="#dashboard-activities"
                        hx-swap="innerHTML"
                        class="text-sm text-blue-600 hover:text-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>
        <div id="dashboard-activities" 
             hx-get="{% url 'dashboard_activities_htmx' %}" 
             hx-trigger="load, every 30s"
             hx-swap="innerHTML">
            {% include 'partials/dashboard_activities.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // GSO Dashboard-specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize GSO navigation
        initGSONavigation();
        
        // Add loading states for HTMX requests
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            const target = evt.target;
            if (target.id === 'dashboard-stats' || target.id === 'dashboard-widgets' || target.id === 'dashboard-activities') {
                target.style.opacity = '0.7';
            }
        });
        
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const target = evt.target;
            if (target.id === 'dashboard-stats' || target.id === 'dashboard-widgets' || target.id === 'dashboard-activities') {
                target.style.opacity = '1';
            }
        });
        
        // GSO-specific: More frequent updates for pending approvals
        setInterval(function() {
            const activitiesElement = document.getElementById('dashboard-activities');
            if (activitiesElement) {
                htmx.trigger(activitiesElement, 'refresh');
            }
        }, 20000); // Refresh every 20 seconds for GSO staff
    });
    
    function initGSONavigation() {
        // Set active navigation item based on current URL
        setActiveNavItem();
        
        // Initialize submenu toggles
        initSubmenuToggles();
        
        // Initialize navigation state persistence
        initNavStatePersistence();
    }
    
    function setActiveNavItem() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link, .nav-sublink');
        
        // Remove all active states
        navLinks.forEach(link => {
            link.classList.remove('bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
            link.classList.add('text-gray-700');
        });
        
        // Find and activate current page
        let activeLink = null;
        let activeSection = null;
        
        // Check for exact matches first
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath === href) {
                activeLink = link;
                activeSection = link.closest('.nav-section');
            }
        });
        
        // If no exact match, check for partial matches (for detail pages)
        if (!activeLink) {
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && currentPath.startsWith(href) && href !== '/gso/') {
                    if (!activeLink || href.length > activeLink.getAttribute('href').length) {
                        activeLink = link;
                        activeSection = link.closest('.nav-section');
                    }
                }
            });
        }
        
        // Apply active styles
        if (activeLink) {
            activeLink.classList.remove('text-gray-700');
            activeLink.classList.add('bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
            
            // If it's a sublink, also highlight the parent section
            if (activeLink.classList.contains('nav-sublink') && activeSection) {
                const parentLink = activeSection.querySelector('.nav-link');
                if (parentLink) {
                    parentLink.classList.remove('text-gray-700');
                    parentLink.classList.add('bg-blue-50', 'text-blue-600');
                    
                    // Show the submenu
                    const submenu = activeSection.querySelector('.nav-submenu');
                    if (submenu) {
                        submenu.classList.remove('hidden');
                        const arrow = parentLink.querySelector('svg:last-child');
                        if (arrow) {
                            arrow.classList.add('rotate-180');
                        }
                    }
                }
            }
        }
    }
    
    function initSubmenuToggles() {
        const navSections = document.querySelectorAll('.nav-section');
        
        navSections.forEach(section => {
            const mainLink = section.querySelector('.nav-link');
            const submenu = section.querySelector('.nav-submenu');
            const arrow = mainLink?.querySelector('svg:last-child');
            
            if (mainLink && submenu) {
                mainLink.addEventListener('click', function(e) {
                    // Only prevent default if clicking on the arrow area or if submenu is closed
                    const isArrowClick = e.target.closest('svg:last-child');
                    const isSubmenuHidden = submenu.classList.contains('hidden');
                    
                    if (isArrowClick || isSubmenuHidden) {
                        e.preventDefault();
                        
                        // Toggle submenu
                        submenu.classList.toggle('hidden');
                        
                        // Rotate arrow
                        if (arrow) {
                            arrow.classList.toggle('rotate-180');
                        }
                        
                        // Save state
                        const sectionName = section.getAttribute('data-nav-section');
                        if (sectionName) {
                            localStorage.setItem(`gso-nav-${sectionName}`, !submenu.classList.contains('hidden'));
                        }
                    }
                });
            }
        });
    }
    
    function initNavStatePersistence() {
        // Restore submenu states from localStorage
        const navSections = document.querySelectorAll('.nav-section');
        
        navSections.forEach(section => {
            const sectionName = section.getAttribute('data-nav-section');
            if (sectionName) {
                const isOpen = localStorage.getItem(`gso-nav-${sectionName}`) === 'true';
                const submenu = section.querySelector('.nav-submenu');
                const arrow = section.querySelector('.nav-link svg:last-child');
                
                if (isOpen && submenu) {
                    submenu.classList.remove('hidden');
                    if (arrow) {
                        arrow.classList.add('rotate-180');
                    }
                }
            }
        });
    }
</script>
{% endblock %}
