"""
Notification system views
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from django.contrib import messages
import json

from .models import Notification, NotificationPreference, SupplyItem, SupplyRequest
from .decorators import role_required


@login_required
def notification_center(request):
    """Main notification center view"""
    # Get user's notifications
    notifications = Notification.objects.filter(
        recipient=request.user,
        is_dismissed=False
    ).select_related('supply_item', 'supply_request').order_by('-created_at')
    
    # Filter by type if specified
    notification_type = request.GET.get('type', '')
    if notification_type:
        notifications = notifications.filter(notification_type=notification_type)
    
    # Filter by read status
    read_status = request.GET.get('read', '')
    if read_status == 'unread':
        notifications = notifications.filter(is_read=False)
    elif read_status == 'read':
        notifications = notifications.filter(is_read=True)
    
    # Pagination
    paginator = Paginator(notifications, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get notification counts
    unread_count = Notification.objects.filter(
        recipient=request.user,
        is_read=False,
        is_dismissed=False
    ).count()
    
    total_count = Notification.objects.filter(
        recipient=request.user,
        is_dismissed=False
    ).count()
    
    # Get notification type counts
    type_counts_query = Notification.objects.filter(
        recipient=request.user,
        is_dismissed=False
    ).values('notification_type').annotate(count=Count('id'))

    # Create a safe type_counts dictionary with defaults
    type_counts = {}
    for item in type_counts_query:
        type_counts[item['notification_type']] = item['count']

    # Ensure all notification types have a count (default to 0)
    for notification_type, _ in Notification.NOTIFICATION_TYPES:
        if notification_type not in type_counts:
            type_counts[notification_type] = 0

    context = {
        'page_obj': page_obj,
        'unread_count': unread_count,
        'total_count': total_count,
        'type_counts': type_counts,
        'current_type': notification_type,
        'current_read_status': read_status,
        'notification_types': Notification.NOTIFICATION_TYPES,
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'notifications/partials/notification_list.html', context)
    
    return render(request, 'notifications/notification_center.html', context)


@login_required
@require_http_methods(["POST"])
def mark_notification_read(request, notification_id):
    """Mark a notification as read"""
    notification = get_object_or_404(
        Notification,
        id=notification_id,
        recipient=request.user
    )
    
    notification.mark_as_read()
    
    if request.headers.get('HX-Request'):
        return render(request, 'notifications/partials/notification_item.html', {
            'notification': notification
        })
    
    return JsonResponse({'success': True})


@login_required
@require_http_methods(["POST"])
def dismiss_notification(request, notification_id):
    """Dismiss a notification"""
    notification = get_object_or_404(
        Notification,
        id=notification_id,
        recipient=request.user
    )
    
    notification.dismiss()
    
    if request.headers.get('HX-Request'):
        return HttpResponse('')  # Remove the notification from the list
    
    return JsonResponse({'success': True})


@login_required
@require_http_methods(["POST"])
def mark_all_read(request):
    """Mark all notifications as read"""
    updated = Notification.objects.filter(
        recipient=request.user,
        is_read=False,
        is_dismissed=False
    ).update(is_read=True, read_at=timezone.now())
    
    messages.success(request, f'Marked {updated} notifications as read.')
    
    if request.headers.get('HX-Request'):
        return redirect('notification_center')
    
    return JsonResponse({'success': True, 'updated': updated})


@login_required
@require_http_methods(["POST"])
def dismiss_all_read(request):
    """Dismiss all read notifications"""
    updated = Notification.objects.filter(
        recipient=request.user,
        is_read=True,
        is_dismissed=False
    ).update(is_dismissed=True)
    
    messages.success(request, f'Dismissed {updated} read notifications.')
    
    if request.headers.get('HX-Request'):
        return redirect('notification_center')
    
    return JsonResponse({'success': True, 'updated': updated})


@login_required
def notification_preferences(request):
    """Manage notification preferences"""
    preferences, created = NotificationPreference.objects.get_or_create(
        user=request.user
    )
    
    if request.method == 'POST':
        # Update preferences
        preferences.low_stock_alerts = request.POST.get('low_stock_alerts') == 'on'
        preferences.out_of_stock_alerts = request.POST.get('out_of_stock_alerts') == 'on'
        preferences.request_status_updates = request.POST.get('request_status_updates') == 'on'
        preferences.system_alerts = request.POST.get('system_alerts') == 'on'
        preferences.maintenance_notices = request.POST.get('maintenance_notices') == 'on'
        
        preferences.in_app_notifications = request.POST.get('in_app_notifications') == 'on'
        preferences.email_notifications = request.POST.get('email_notifications') == 'on'
        
        preferences.digest_frequency = request.POST.get('digest_frequency', 'immediate')
        
        preferences.quiet_hours_enabled = request.POST.get('quiet_hours_enabled') == 'on'
        
        if preferences.quiet_hours_enabled:
            quiet_start = request.POST.get('quiet_hours_start')
            quiet_end = request.POST.get('quiet_hours_end')
            
            if quiet_start:
                preferences.quiet_hours_start = quiet_start
            if quiet_end:
                preferences.quiet_hours_end = quiet_end
        
        preferences.save()
        messages.success(request, 'Notification preferences updated successfully.')
        
        if request.headers.get('HX-Request'):
            return render(request, 'notifications/partials/preferences_form.html', {
                'preferences': preferences,
                'success_message': 'Preferences updated successfully!'
            })
        
        return redirect('notification_preferences')
    
    context = {
        'preferences': preferences,
    }
    
    return render(request, 'notifications/notification_preferences.html', context)


@login_required
def live_notification_count(request):
    """Get live notification count for navbar"""
    unread_count = Notification.objects.filter(
        recipient=request.user,
        is_read=False,
        is_dismissed=False
    ).count()
    
    return render(request, 'notifications/partials/notification_badge.html', {
        'unread_count': unread_count
    })


@login_required
def notification_dropdown(request):
    """Get recent notifications for dropdown"""
    notifications = Notification.objects.filter(
        recipient=request.user,
        is_dismissed=False
    ).select_related('supply_item', 'supply_request').order_by('-created_at')[:5]
    
    unread_count = Notification.objects.filter(
        recipient=request.user,
        is_read=False,
        is_dismissed=False
    ).count()
    
    return render(request, 'notifications/partials/notification_dropdown.html', {
        'notifications': notifications,
        'unread_count': unread_count
    })


@login_required
@role_required(['admin', 'gso_staff'])
def create_system_notification(request):
    """Create system-wide notifications"""
    if request.method == 'POST':
        title = request.POST.get('title', '').strip()
        message = request.POST.get('message', '').strip()
        notification_type = request.POST.get('notification_type', 'system_alert')
        priority = request.POST.get('priority', 'normal')
        target_roles = request.POST.getlist('target_roles')
        expires_in_days = request.POST.get('expires_in_days', '')
        
        if not title or not message:
            messages.error(request, 'Title and message are required.')
            return render(request, 'notifications/create_system_notification.html')
        
        # Get target users
        if target_roles:
            from .models import UserProfile
            target_users = UserProfile.objects.filter(
                role__in=target_roles
            ).values_list('user', flat=True)
        else:
            from django.contrib.auth.models import User
            target_users = User.objects.filter(is_active=True).values_list('id', flat=True)
        
        # Create notifications
        expires_days = None
        if expires_in_days:
            try:
                expires_days = int(expires_in_days)
            except ValueError:
                pass
        
        created_count = 0
        for user_id in target_users:
            from django.contrib.auth.models import User
            user = User.objects.get(id=user_id)
            Notification.create_notification(
                recipient=user,
                notification_type=notification_type,
                title=title,
                message=message,
                priority=priority,
                expires_in_days=expires_days
            )
            created_count += 1
        
        messages.success(request, f'Created {created_count} notifications.')
        return redirect('create_system_notification')
    
    from .models import UserProfile
    context = {
        'notification_types': Notification.NOTIFICATION_TYPES,
        'priority_levels': Notification.PRIORITY_LEVELS,
        'user_roles': UserProfile.ROLE_CHOICES,
    }
    
    return render(request, 'notifications/create_system_notification.html', context)
