<div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4">
    <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Stock Adjustment</h4>
    
    <!-- Error message -->
    {% if error_message %}
        <div class="mb-3 bg-red-50 border border-red-200 rounded-md p-3">
            <div class="flex">
                <svg class="h-4 w-4 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-2">
                    <p class="text-sm text-red-800">{{ error_message }}</p>
                </div>
            </div>
        </div>
    {% endif %}
    
    <form hx-post="{% url 'quick_stock_adjustment' supply_item.id %}"
          hx-target="#supply-item-{{ supply_item.id }}"
          hx-swap="outerHTML"
          hx-indicator="#adjustment-loading"
          class="space-y-3">
        {% csrf_token %}
        
        <!-- Loading indicator -->
        <div id="adjustment-loading" class="htmx-indicator">
            <div class="flex items-center justify-center py-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span class="ml-2 text-sm text-gray-600">Adjusting stock...</span>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <!-- Adjustment Type -->
            <div>
                <label for="adjustment_type" class="block text-xs font-medium text-gray-700 mb-1">
                    Action
                </label>
                <select name="adjustment_type" id="adjustment_type" required
                        class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="add">Add Stock</option>
                    <option value="subtract">Remove Stock</option>
                    <option value="set">Set Stock To</option>
                </select>
            </div>
            
            <!-- Quantity -->
            <div>
                <label for="quantity" class="block text-xs font-medium text-gray-700 mb-1">
                    Quantity
                </label>
                <input type="number" name="quantity" id="quantity" min="1" required
                       class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Enter quantity">
            </div>
            
            <!-- Current Stock Display -->
            <div>
                <label class="block text-xs font-medium text-gray-700 mb-1">
                    Current Stock
                </label>
                <div class="w-full px-2 py-1 text-sm bg-gray-100 border border-gray-300 rounded-md">
                    {{ supply_item.current_stock }} {{ supply_item.unit_of_measure }}
                </div>
            </div>
        </div>
        
        <!-- Notes -->
        <div>
            <label for="notes" class="block text-xs font-medium text-gray-700 mb-1">
                Notes (Optional)
            </label>
            <input type="text" name="notes" id="notes"
                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Reason for adjustment">
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-end space-x-2 pt-2">
            <button type="button" 
                    hx-get="{% url 'supply_item_detail' supply_item.id %}"
                    hx-target="#supply-item-{{ supply_item.id }}"
                    hx-swap="outerHTML"
                    class="px-3 py-1 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Cancel
            </button>
            
            <button type="submit" 
                    class="px-3 py-1 text-xs font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Apply Adjustment
            </button>
        </div>
    </form>
</div>
