{% if grouped_searches %}
    {% for search_type, searches in grouped_searches.items %}
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    {% for value, label in search_type_choices %}
                        {% if value == search_type %}{{ label }}{% endif %}
                    {% endfor %}
                </h3>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for search in searches %}
                        <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                            <!-- Search Header -->
                            <div class="flex justify-between items-start mb-3">
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-900 mb-1">{{ search.name }}</h4>
                                    <p class="text-sm text-gray-600">
                                        {% if search.user == user %}
                                            My Search
                                        {% else %}
                                            Public • {{ search.user.get_full_name|default:search.user.username }}
                                        {% endif %}
                                    </p>
                                </div>
                                
                                {% if search.is_public %}
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                        Public
                                    </span>
                                {% endif %}
                            </div>
                            
                            <!-- Search Details -->
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Used:</span>
                                    <span class="font-medium">{{ search.usage_count }} time{{ search.usage_count|pluralize }}</span>
                                </div>
                                
                                {% if search.last_used %}
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">Last used:</span>
                                        <span class="font-medium">{{ search.last_used|date:"M d, Y" }}</span>
                                    </div>
                                {% endif %}
                                
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Created:</span>
                                    <span class="font-medium">{{ search.created_at|date:"M d, Y" }}</span>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="mt-4 flex space-x-2">
                                <button onclick="loadSavedSearch({{ search.id }})" 
                                        class="flex-1 text-center px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                    Load Search
                                </button>
                                
                                {% if search.user == user %}
                                    <button onclick="deleteSavedSearch({{ search.id }})" 
                                            class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                        Delete
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    {% endfor %}
{% else %}
    <!-- No Saved Searches -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No saved searches</h3>
            <p class="text-gray-600 mb-4">Save your frequently used search queries for quick access.</p>
            <a href="{% url 'advanced_inventory_search' %}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Start Searching
            </a>
        </div>
    </div>
{% endif %}

<script>
function loadSavedSearch(searchId) {
    fetch(`/search/load/${searchId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.url;
            } else {
                alert('Error loading search: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error loading search: ' + error);
        });
}

function deleteSavedSearch(searchId) {
    if (confirm('Are you sure you want to delete this saved search?')) {
        fetch(`/search/delete/${searchId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error deleting search: ' + data.error);
            }
        })
        .catch(error => {
            alert('Error deleting search: ' + error);
        });
    }
}
</script>
