"""
Management command for backing up system data
"""
import os
import json
import zipfile
from datetime import datetime
from django.core.management.base import BaseCommand
from django.core import serializers
from django.conf import settings
from django.apps import apps

from suptrack.models import (
    UserProfile, SupplyCategory, SupplyItem, SupplyRequest,
    RequestItem, QRScanLog, InventoryTransaction, SavedSearch, SystemConfiguration
)


class Command(BaseCommand):
    help = 'Create a backup of all system data'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--output-dir',
            type=str,
            default='backups',
            help='Directory to store backup files (default: backups)'
        )
        parser.add_argument(
            '--include-media',
            action='store_true',
            help='Include media files (QR codes, etc.) in backup'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['json', 'xml'],
            default='json',
            help='Backup format (default: json)'
        )
    
    def handle(self, *args, **options):
        output_dir = options['output_dir']
        include_media = options['include_media']
        backup_format = options['format']
        
        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'smart_supply_backup_{timestamp}.zip'
        backup_path = os.path.join(output_dir, backup_filename)
        
        self.stdout.write(f'Creating backup: {backup_path}')
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as backup_zip:
            # Backup database data
            self._backup_database_data(backup_zip, backup_format)
            
            # Backup media files if requested
            if include_media:
                self._backup_media_files(backup_zip)
            
            # Create backup manifest
            self._create_backup_manifest(backup_zip, include_media, backup_format)
        
        self.stdout.write(
            self.style.SUCCESS(f'Backup created successfully: {backup_path}')
        )
    
    def _backup_database_data(self, backup_zip, backup_format):
        """Backup all database data"""
        models_to_backup = [
            UserProfile, SupplyCategory, SupplyItem, SupplyRequest,
            RequestItem, QRScanLog, InventoryTransaction, SavedSearch, SystemConfiguration
        ]
        
        for model in models_to_backup:
            model_name = model._meta.label_lower.replace('.', '_')
            filename = f'data/{model_name}.{backup_format}'
            
            self.stdout.write(f'Backing up {model._meta.verbose_name_plural}...')
            
            # Serialize model data
            queryset = model.objects.all()
            if backup_format == 'json':
                serialized_data = serializers.serialize('json', queryset, indent=2)
            else:
                serialized_data = serializers.serialize('xml', queryset)
            
            # Add to zip file
            backup_zip.writestr(filename, serialized_data)
            
            self.stdout.write(f'  - {queryset.count()} records backed up')
    
    def _backup_media_files(self, backup_zip):
        """Backup media files (QR codes, etc.)"""
        media_root = settings.MEDIA_ROOT
        
        if not os.path.exists(media_root):
            self.stdout.write('No media directory found, skipping media backup')
            return
        
        self.stdout.write('Backing up media files...')
        
        for root, dirs, files in os.walk(media_root):
            for file in files:
                file_path = os.path.join(root, file)
                # Get relative path from media root
                arcname = os.path.join('media', os.path.relpath(file_path, media_root))
                backup_zip.write(file_path, arcname)
        
        self.stdout.write(f'  - Media files backed up')
    
    def _create_backup_manifest(self, backup_zip, include_media, backup_format):
        """Create backup manifest with metadata"""
        manifest = {
            'backup_date': datetime.now().isoformat(),
            'django_version': getattr(settings, 'DJANGO_VERSION', 'unknown'),
            'database_engine': settings.DATABASES['default']['ENGINE'],
            'format': backup_format,
            'include_media': include_media,
            'models': []
        }
        
        # Add model information
        models_to_backup = [
            UserProfile, SupplyCategory, SupplyItem, SupplyRequest,
            RequestItem, QRScanLog, InventoryTransaction, SavedSearch, SystemConfiguration
        ]
        
        for model in models_to_backup:
            manifest['models'].append({
                'name': model._meta.label_lower,
                'verbose_name': model._meta.verbose_name,
                'count': model.objects.count()
            })
        
        # Write manifest to backup
        manifest_json = json.dumps(manifest, indent=2)
        backup_zip.writestr('manifest.json', manifest_json)
